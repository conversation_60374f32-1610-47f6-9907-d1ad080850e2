<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Description Review System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .progress-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            min-height: 600px;
        }

        .preview-panel {
            padding: 30px;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto;
            max-height: 80vh;
        }

        .review-panel {
            padding: 30px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .product-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .product-info h3 {
            color: #1976d2;
            margin-bottom: 8px;
        }

        .product-info p {
            color: #666;
            font-size: 14px;
        }

        .html-content {
            background: white;
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            line-height: 1.6;
        }

        .html-content h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .html-content h2 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 22px;
        }

        .html-content h3 {
            color: #7f8c8d;
            margin: 20px 0 10px 0;
            font-size: 18px;
        }

        .html-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .html-content table th,
        .html-content table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .html-content table th {
            background-color: #867e7b;
            color: white;
        }

        .review-form {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .review-section {
            margin-bottom: 25px;
        }

        .review-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .status-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .status-btn {
            padding: 12px;
            border: 2px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-btn.approve {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .status-btn.reject {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .status-btn.pending {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .status-btn.active {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .comment-area {
            flex: 1;
            min-height: 120px;
        }

        .comment-area textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .comment-area textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .nav-btn.prev {
            background: #6c757d;
            color: white;
        }

        .nav-btn.next {
            background: #007bff;
            color: white;
        }

        .nav-btn.save {
            background: #28a745;
            color: white;
        }

        .nav-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .item-counter {
            font-weight: 600;
            color: #495057;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .preview-panel {
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 AI Description Review System</h1>
            <div class="progress-info">
                <span id="progressText">Item 1 of 10</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="preview-panel">
                <div class="product-info">
                    <h3 id="productName">Loading...</h3>
                    <p id="productDetails">Excel Row: - | Category: -</p>
                </div>
                
                <div class="html-content" id="htmlContent">
                    <div class="loading">
                        <p>Loading content...</p>
                    </div>
                </div>
            </div>

            <div class="review-panel">
                <div class="review-form">
                    <div class="review-section">
                        <h3>📋 Review Status</h3>
                        <div class="status-buttons">
                            <div class="status-btn approve" data-status="approved">
                                ✅ Approve
                            </div>
                            <div class="status-btn reject" data-status="rejected">
                                ❌ Reject
                            </div>
                            <div class="status-btn pending" data-status="pending">
                                ⏳ Pending
                            </div>
                        </div>
                    </div>

                    <div class="review-section">
                        <h3>💬 Comments</h3>
                        <div class="comment-area">
                            <textarea id="reviewComment" placeholder="Enter your review comments here...&#10;&#10;Examples:&#10;- Great description, approved!&#10;- Need to add more technical details&#10;- Image analysis could be more specific&#10;- Perfect for SEO optimization"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <button class="nav-btn prev" id="prevBtn">
                ← Previous
            </button>
            
            <div class="item-counter">
                <span id="itemCounter">Item 1 of 10</span>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button class="nav-btn save" id="saveBtn">
                    💾 Save Review
                </button>
                <button class="nav-btn next" id="nextBtn">
                    Next →
                </button>
            </div>
        </div>
    </div>

    <script src="review_system.js"></script>
</body>
</html>
