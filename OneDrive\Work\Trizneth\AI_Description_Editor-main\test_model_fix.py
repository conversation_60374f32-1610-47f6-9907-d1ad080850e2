#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Model Fix
測試模型修復
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_mapping():
    """測試模型映射功能"""
    print("🔍 測試模型映射功能...")
    
    try:
        # 測試處理引擎的模型映射
        from core.processing_engine import ProcessingEngine
        
        # 創建一個簡單的處理引擎實例來測試映射
        class MockProcessingEngine:
            def _get_model_key(self, model_display_name: str) -> str:
                """模型顯示名稱到鍵值的映射"""
                model_mapping = [
                    # 按長度排序，避免部分匹配問題
                    ('GPT-4o mini', 'openai-gpt4o-mini'),
                    ('GPT-4o', 'openai-gpt4o'),
                    ('Claude Opus 4', 'anthropic-opus4'),
                    ('Claude Sonnet 4', 'anthropic-sonnet4'),
                    ('Gemini 2.5 Pro', 'google-pro25'),
                    ('Gemini 2.5 Flash', 'google-flash25')
                ]

                # 嘗試按順序匹配（避免部分匹配問題）
                for model_name, model_key in model_mapping:
                    if model_name in model_display_name:
                        return model_key
                
                # 如果沒有找到映射，嘗試舊的格式
                if 'openai' in model_display_name.lower():
                    return 'openai-gpt4o'
                elif 'anthropic' in model_display_name.lower() or 'claude' in model_display_name.lower():
                    return 'anthropic-sonnet4'
                elif 'google' in model_display_name.lower() or 'gemini' in model_display_name.lower():
                    return 'google-pro25'
                
                return model_display_name
        
        mock_engine = MockProcessingEngine()
        
        # 測試映射
        test_cases = [
            ('GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer', 'openai-gpt4o'),
            ('GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer', 'openai-gpt4o-mini'),
            ('Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer', 'anthropic-sonnet4'),
            ('Claude Opus 4 | Anthropic | ✅精準圖片 | 💰💰💰高成本 | 🌟🌟🌟🌟🌟精緻度 | Editor（高品質需求）', 'anthropic-opus4'),
            ('Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer', 'google-pro25'),
            ('Gemini 2.5 Flash | Google | ✅圖片理解 | 💰經濟 | 🌟🌟🌟精緻度 | Reviewer快速審查', 'google-flash25')
        ]
        
        print("\n📊 模型映射測試:")
        all_passed = True
        for display_name, expected_key in test_cases:
            mapped_key = mock_engine._get_model_key(display_name)
            passed = mapped_key == expected_key
            status = "✅" if passed else "❌"
            print(f"   {status} '{display_name[:30]}...' → '{mapped_key}' (期望: '{expected_key}')")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 模型映射測試失敗: {e}")
        return False

def test_gui_mapping():
    """測試 GUI 映射功能"""
    print("\n🔍 測試 GUI 映射功能...")
    
    try:
        # 模擬 GUI 的映射方法
        class MockMainWindow:
            def _map_display_name_to_key(self, display_name: str) -> str:
                """將顯示名稱映射到模型鍵值"""
                model_mapping = [
                    # 按長度排序，避免部分匹配問題
                    ('GPT-4o mini', 'openai-gpt4o-mini'),
                    ('GPT-4o', 'openai-gpt4o'),
                    ('Claude Opus 4', 'anthropic-opus4'),
                    ('Claude Sonnet 4', 'anthropic-sonnet4'),
                    ('Gemini 2.5 Pro', 'google-pro25'),
                    ('Gemini 2.5 Flash', 'google-flash25')
                ]

                # 嘗試按順序匹配（避免部分匹配問題）
                for model_name, model_key in model_mapping:
                    if model_name in display_name:
                        return model_key
                
                # 如果沒有找到映射，嘗試從顯示名稱推斷
                if 'GPT-4o mini' in display_name or 'gpt-4o-mini' in display_name.lower():
                    return 'openai-gpt4o-mini'
                elif 'GPT-4o' in display_name or 'gpt-4o' in display_name.lower():
                    return 'openai-gpt4o'
                elif 'Claude Opus 4' in display_name or 'opus 4' in display_name.lower():
                    return 'anthropic-opus4'
                elif 'Claude Sonnet 4' in display_name or 'sonnet 4' in display_name.lower():
                    return 'anthropic-sonnet4'
                elif 'Gemini 2.5 Pro' in display_name or '2.5 pro' in display_name.lower():
                    return 'google-pro25'
                elif 'Gemini 2.5 Flash' in display_name or '2.5 flash' in display_name.lower():
                    return 'google-flash25'
                
                return 'openai-gpt4o-mini'  # 預設值
        
        mock_window = MockMainWindow()
        
        # 測試 GUI 映射
        test_cases = [
            ('GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer', 'openai-gpt4o'),
            ('GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer', 'openai-gpt4o-mini'),
            ('Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer', 'anthropic-sonnet4'),
            ('Claude Opus 4 | Anthropic | ✅精準圖片 | 💰💰💰高成本 | 🌟🌟🌟🌟🌟精緻度 | Editor（高品質需求）', 'anthropic-opus4'),
            ('Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer', 'google-pro25'),
            ('Gemini 2.5 Flash | Google | ✅圖片理解 | 💰經濟 | 🌟🌟🌟精緻度 | Reviewer快速審查', 'google-flash25')
        ]
        
        print("\n📊 GUI 映射測試:")
        all_passed = True
        for display_name, expected_key in test_cases:
            mapped_key = mock_window._map_display_name_to_key(display_name)
            passed = mapped_key == expected_key
            status = "✅" if passed else "❌"
            print(f"   {status} '{display_name[:30]}...' → '{mapped_key}' (期望: '{expected_key}')")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ GUI 映射測試失敗: {e}")
        return False

def test_cost_calculator_keys():
    """測試成本計算器的模型鍵值"""
    print("\n🔍 測試成本計算器模型鍵值...")
    
    try:
        from core.cost_calculator import CostCalculator
        
        calculator = CostCalculator()
        
        expected_keys = [
            'openai-gpt4o',
            'openai-gpt4o-mini',
            'anthropic-opus4',
            'anthropic-sonnet4',
            'google-pro25',
            'google-flash25'
        ]
        
        print("\n📊 成本計算器模型鍵值檢查:")
        all_passed = True
        for key in expected_keys:
            exists = key in calculator.pricing
            status = "✅" if exists else "❌"
            print(f"   {status} {key}: {'存在' if exists else '不存在'}")
            if not exists:
                all_passed = False
        
        # 檢查是否有舊的鍵值
        old_keys = ['anthropic-sonnet', 'anthropic-opus', 'anthropic-haiku', 'openai-gpt35', 'google-pro', 'google-flash']
        print("\n🔍 檢查舊模型鍵值:")
        for key in old_keys:
            exists = key in calculator.pricing
            status = "⚠️" if exists else "✅"
            print(f"   {status} {key}: {'仍存在（應移除）' if exists else '已移除'}")
            if exists:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 成本計算器測試失敗: {e}")
        return False

def show_fix_summary():
    """顯示修復總結"""
    print("\n🔧 修復總結:")
    print("\n✅ **已修復的問題**:")
    print("1. 更新處理引擎的預設模型鍵值映射")
    print("2. 修復 GUI 的預設選擇邏輯")
    print("3. 添加 GUI 的顯示名稱到模型鍵值映射方法")
    print("4. 確保所有新模型鍵值在成本計算器中存在")
    
    print("\n🎯 **關鍵修復點**:")
    print("• 'anthropic-sonnet' → 'anthropic-sonnet4'")
    print("• 'anthropic-opus' → 'anthropic-opus4'")
    print("• 'google-pro' → 'google-pro25'")
    print("• 'google-flash' → 'google-flash25'")
    print("• 新增 'openai-gpt4o-mini'")
    
    print("\n🚀 **下一步**:")
    print("1. 重新啟動程式: python main.py")
    print("2. 選擇任何 AI 模型進行測試")
    print("3. 開始處理，應該不會再出現 '模型不可用' 錯誤")

def main():
    """主測試函數"""
    print("🚀 模型修復測試")
    print("=" * 50)
    
    tests = [
        ("處理引擎模型映射", test_model_mapping),
        ("GUI 模型映射", test_gui_mapping),
        ("成本計算器模型鍵值", test_cost_calculator_keys),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    show_fix_summary()
    
    print("\n" + "=" * 50)
    print(f"📊 修復測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有修復測試通過！模型鍵值問題已解決。")
        print("\n🚀 現在可以正常使用程式了:")
        print("1. 啟動程式: python main.py")
        print("2. 選擇任何 AI 模型")
        print("3. 開始處理，不會再出現模型不可用錯誤")
    else:
        print("⚠️ 部分修復測試失敗，請檢查錯誤訊息。")
    
    return passed == total

if __name__ == "__main__":
    main()
