#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Enhanced AI Model Selection
測試增強的 AI 模型選擇功能
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_enhanced_models():
    """測試增強的 AI 模型功能"""
    print("🔍 測試增強的 AI 模型選擇功能...")
    
    try:
        from core.cost_calculator import CostCalculator
        from core.ai_models import AIModelManager
        from config.settings import load_config
        
        # 測試成本計算器的新定價
        calculator = CostCalculator()
        print("✅ 成本計算器已載入")
        
        print("\n📊 支援的 AI 模型和定價:")
        for model_key, pricing in calculator.pricing.items():
            display_name = pricing['display_name']
            features = pricing.get('features', '')
            input_cost = pricing['input'] * 1000  # 轉換為每 1K tokens
            output_cost = pricing['output'] * 1000
            
            print(f"\n🤖 **{display_name}**")
            print(f"   - 模型鍵值: {model_key}")
            print(f"   - 特色: {features}")
            print(f"   - 輸入成本: ${input_cost:.4f} per 1K tokens")
            print(f"   - 輸出成本: ${output_cost:.4f} per 1K tokens")
            
            if pricing.get('free_tier'):
                free_limit = pricing.get('free_limit', 0)
                print(f"   - 免費額度: {free_limit:,} tokens/月")
        
        # 測試成本計算
        print("\n💰 成本計算測試 (1000 input + 500 output tokens):")
        test_usage = [
            ('openai-gpt4o', 'GPT-4o'),
            ('openai-gpt35', 'GPT-3.5 Turbo'),
            ('anthropic-opus', 'Claude 3 Opus'),
            ('anthropic-sonnet', 'Claude 3 Sonnet'),
            ('anthropic-haiku', 'Claude 3 Haiku'),
            ('google-pro', 'Gemini 1.5 Pro'),
            ('google-flash', 'Gemini 1.5 Flash')
        ]
        
        costs = []
        for model_key, display_name in test_usage:
            if model_key in calculator.pricing:
                cost = calculator.calculate_cost(model_key, 1000, 500)
                costs.append((display_name, cost))
                print(f"   {display_name}: ${cost:.6f}")
        
        # 排序顯示最經濟的選擇
        costs.sort(key=lambda x: x[1])
        print(f"\n🏆 最經濟選擇: {costs[0][0]} (${costs[0][1]:.6f})")
        print(f"🥈 次經濟選擇: {costs[1][0]} (${costs[1][1]:.6f})")
        
        # 測試 AI 模型管理器
        config = load_config()
        ai_manager = AIModelManager(config, calculator)
        print(f"\n✅ AI 模型管理器已載入")
        
        available_models = ai_manager.get_available_models()
        print(f"📋 可用模型: {available_models}")
        
        print("\n🎉 增強模型功能測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_model_recommendations():
    """顯示模型推薦配置"""
    print("\n💡 AI 模型推薦配置:")
    
    print("\n🥇 **經濟實惠配置** (日常使用):")
    print("   Writer: GPT-3.5 Turbo 或 Claude 3 Haiku")
    print("   Reviewer: GPT-3.5 Turbo 或 Gemini 1.5 Flash")
    print("   預估成本: ~$0.001-0.002 per product")
    print("   適用: 大量產品處理，預算有限")
    
    print("\n🥈 **平衡配置** (推薦):")
    print("   Writer: Claude 3 Sonnet 或 Gemini 1.5 Pro")
    print("   Reviewer: Claude 3 Sonnet 或 GPT-4o")
    print("   預估成本: ~$0.003-0.008 per product")
    print("   適用: 品質與成本平衡")
    
    print("\n🥉 **高品質配置** (精品內容):")
    print("   Writer: Claude 3 Opus 或 GPT-4o")
    print("   Reviewer: Claude 3 Opus")
    print("   預估成本: ~$0.015-0.030 per product")
    print("   適用: 高價值產品，品質要求極高")
    
    print("\n🆓 **免費測試配置**:")
    print("   Writer: Gemini 1.5 Pro (免費額度)")
    print("   Reviewer: Gemini 1.5 Flash (免費額度)")
    print("   預估成本: $0.00 (免費額度內)")
    print("   適用: 測試和開發階段")
    
    print("\n🎯 **圖片處理配置**:")
    print("   Writer: GPT-4o (最佳圖片理解)")
    print("   Reviewer: Claude 3 Opus (精準圖片分析)")
    print("   預估成本: ~$0.020-0.040 per product")
    print("   適用: 需要圖片分析的產品")

def show_role_recommendations():
    """顯示角色推薦"""
    print("\n📋 模型角色推薦:")
    
    print("\n✍️ **Writer (編輯) 推薦**:")
    print("   🌟🌟🌟🌟🌟 Claude 3 Opus - 最高創意和品質")
    print("   🌟🌟🌟🌟🌟 GPT-4o - 最佳圖片理解")
    print("   🌟🌟🌟🌟🌟 Gemini 1.5 Pro - 強大圖片理解 + 免費額度")
    print("   🌟🌟🌟🌟   Claude 3 Sonnet - 平衡選擇")
    
    print("\n🔍 **Reviewer (審查) 推薦**:")
    print("   🌟🌟🌟🌟🌟 Claude 3 Opus - 最精準分析")
    print("   🌟🌟🌟🌟🌟 GPT-4o - 全面品質評估")
    print("   🌟🌟🌟🌟🌟 Gemini 1.5 Pro - 強大分析能力")
    print("   🌟🌟🌟🌟   Claude 3 Sonnet - 平衡評估")
    print("   🌟🌟🌟     GPT-3.5 Turbo - 經濟評估")
    print("   🌟🌟🌟     Claude 3 Haiku - 快速評估")
    print("   🌟🌟🌟     Gemini 1.5 Flash - 快速評估")

def main():
    """主測試函數"""
    print("🚀 增強 AI 模型選擇功能測試")
    print("=" * 60)
    
    success = test_enhanced_models()
    show_model_recommendations()
    show_role_recommendations()
    
    if success:
        print("\n✅ 增強模型功能測試成功！")
        print("\n🎯 新功能亮點:")
        print("✅ 7 個專業 AI 模型選擇")
        print("✅ 詳細的模型特色說明")
        print("✅ 角色推薦 (Editor/Reviewer)")
        print("✅ 圖片理解能力標示")
        print("✅ 成本等級和精緻度評級")
        print("✅ 智能模型推薦系統")
        
        print("\n🚀 立即體驗:")
        print("1. 啟動程式: python main.py")
        print("2. 查看新的 AI 模型選擇介面")
        print("3. 根據需求選擇最適合的模型組合")
        print("4. 在成本計算標籤頁監控使用情況")
    else:
        print("\n❌ 測試過程中遇到問題，請檢查錯誤訊息。")
    
    return success

if __name__ == "__main__":
    main()
