#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Import Fix
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        from gui.main_window import MainWindow
        print("✅ MainWindow import successful")
        
        from config.settings import load_config
        print("✅ Config import successful")
        
        # Test QLineEdit specifically
        from PyQt5.QtWidgets import QLineEdit
        print("✅ QLineEdit import successful")
        
        print("\n🎉 All imports fixed!")
        print("🚀 You can now run: python main.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

if __name__ == "__main__":
    test_imports()
