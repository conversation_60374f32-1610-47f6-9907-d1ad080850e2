2025-08-01 18:02:06 | ERROR    | __main__:main:56 | 程式啟動失敗: name 'QLineEdit' is not defined
2025-08-01 18:05:01 | ERROR    | __main__:main:56 | 程式啟動失敗: name 'QLineEdit' is not defined
2025-08-01 21:38:21 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:21 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:40 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:40 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:04 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:04 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:23 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:23 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:47 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:47 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:02 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:02 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:21 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:21 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:39 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:39 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:00 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:00 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:22 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:22 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:42 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:42 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:07 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:07 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:23 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:23 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:44 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:44 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 22:16:44 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:16:44 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:06 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:06 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:48 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:48 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:15 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:15 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:31 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:31 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:51:10 | ERROR    | core.processing_engine:process_batch:398 | 項目處理失敗: Writer 階段失敗: 模型 'anthropic-sonnet' 不可用
2025-08-01 22:51:10 | ERROR    | core.processing_engine:process_batch:398 | 項目處理失敗: Writer 階段失敗: 模型 'anthropic-sonnet' 不可用
2025-08-01 22:51:10 | ERROR    | core.processing_engine:process_batch:398 | 項目處理失敗: Writer 階段失敗: 模型 'anthropic-sonnet' 不可用
2025-08-01 22:51:10 | ERROR    | core.processing_engine:process_batch:398 | 項目處理失敗: Writer 階段失敗: 模型 'anthropic-sonnet' 不可用
