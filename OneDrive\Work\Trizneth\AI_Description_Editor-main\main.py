#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Description Editor - Main Entry Point
AI 商品描述優化系統主程式

Author: AI Assistant
Date: 2025-08-01
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from gui.main_window import MainWindow
from utils.logger import setup_logger
from config.settings import load_config


def main():
    """主程式入口點"""
    # 設定日誌
    logger = setup_logger()
    logger.info("=== AI Description Editor 啟動 ===")
    
    try:
        # 載入配置
        config = load_config()
        logger.info("配置載入完成")
        
        # 建立 Qt 應用程式
        app = QApplication(sys.argv)
        app.setApplicationName("AI Description Editor")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("TriZenith")
        
        # 設定應用程式樣式
        app.setStyle('Fusion')
        
        # 建立主視窗
        main_window = MainWindow(config)
        main_window.show()

        logger.info("GUI 介面已啟動")
        
        # 執行應用程式
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"程式啟動失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
