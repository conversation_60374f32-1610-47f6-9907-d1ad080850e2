#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the static HTML review system
"""

import os
import sys
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_excel():
    """Create a sample Excel file for testing"""
    sample_data = {
        'Product Name': [
            'Vista Dining Chair',
            'Arta Armchair', 
            'Modern Office Chair'
        ],
        'Category': [
            'Furniture',
            'Furniture',
            'Office'
        ],
        'HTML Output': [
            '''<h1>Vista Dining Chair</h1>
<h2>Overview</h2>
<p>Experience the harmony of nature and design with the Vista Dining Chair. Crafted from exquisite white oak, its graceful curves and solid form invite a sense of calm elegance to any dining space.</p>
<h2>Usage & Placement</h2>
<p>Perfect for both intimate dining nooks and spacious dining rooms, the Vista Dining Chair adapts effortlessly to various settings. Its neutral tones and sleek design ensure it complements a wide range of interior styles.</p>
<h2>Design Intent</h2>
<p>Designed with both aesthetics and comfort in mind, the Vista Dining Chair balances a modern silhouette with the timeless beauty of natural wood. Its ergonomic shape supports a relaxed posture, making every meal a luxurious experience.</p>
<table id="brief" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
  <thead>
    <tr style="background-color: #867e7b; color: white;">
      <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Specification</th>
      <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Details</th>
    </tr>
  </thead>
  <tbody>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Dimensions</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">54×62×80 cm</td></tr>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Material</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">White Oak Wood</td></tr>
  </tbody>
</table>''',
            
            '''<h1>Arta Armchair</h1>
<h2>Overview</h2>
<p>The Arta Armchair is a harmonious blend of sumptuous leather and sturdy ash wood, creating a piece that is both visually stunning and invitingly comfortable. Its design speaks of quiet luxury, with a focus on understated elegance and flawless craftsmanship.</p>
<h2>Usage & Placement</h2>
<p>Ideal for refined living spaces, the Arta Armchair offers versatility with its classic appeal. Its neutral tones and sleek form allow it to complement various interior styles, from modern minimalism to classic elegance.</p>
<h2>Design Intent</h2>
<p>Designed with the modern aesthetic in mind, the Arta Armchair combines form and function. Its graceful curves and supportive structure provide a seating experience that is as delightful as it is stylish, emphasizing both posture and poise.</p>
<table id="brief" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
  <thead>
    <tr style="background-color: #867e7b; color: white;">
      <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Specification</th>
      <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Details</th>
    </tr>
  </thead>
  <tbody>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Dimensions</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">60×50×84 cm (Seat Height: 50cm, Armrest Height: 67cm)</td></tr>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Material</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">Beige Leather with White Oak Frame</td></tr>
  </tbody>
</table>''',
            
            '''<h1>Modern Office Chair</h1>
<h2>Overview</h2>
<p>The Modern Office Chair embodies contemporary design philosophy, merging sleek aesthetics with ergonomic functionality. Its clean lines and sophisticated materials create a workspace essential that elevates both comfort and style.</p>
<h2>Usage & Placement</h2>
<p>Perfect for modern offices, home workspaces, and creative studios, this chair adapts seamlessly to professional environments. Its adjustable features and timeless design make it suitable for extended work sessions.</p>
<h2>Design Intent</h2>
<p>Crafted with productivity and wellness in mind, the Modern Office Chair prioritizes proper posture and support. Its minimalist design language speaks to contemporary sensibilities while delivering exceptional comfort.</p>
<table id="brief" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
  <thead>
    <tr style="background-color: #867e7b; color: white;">
      <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Specification</th>
      <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Details</th>
    </tr>
  </thead>
  <tbody>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Seat Width</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">56 cm</td></tr>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Seat Height</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">47 cm</td></tr>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Backrest Height</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">80 cm</td></tr>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Seat Depth</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">46 cm</td></tr>
    <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">Material</td><td style="padding: 8px; border-bottom: 1px solid #ddd;">Beige Fabric with White Oak Frame</td></tr>
  </tbody>
</table>'''
        ]
    }
    
    df = pd.DataFrame(sample_data)
    excel_file = "sample_ai_descriptions.xlsx"
    df.to_excel(excel_file, index=False)
    
    print(f"✅ Created sample Excel file: {excel_file}")
    return excel_file

def test_review_page_generation():
    """Test the review page generation"""
    print("🧪 Testing Static HTML Review System")
    print("=" * 50)
    
    try:
        # Create sample Excel file
        excel_file = create_sample_excel()
        
        # Test review page generation
        from core.review_page_generator import ReviewPageGenerator
        
        generator = ReviewPageGenerator()
        
        # Test basic generation
        print("📄 Generating basic review page...")
        html_file = generator.generate_review_page(excel_file)
        print(f"✅ Generated: {html_file}")
        
        # Test with metadata
        print("📊 Generating review page with metadata...")
        metadata = {
            'total_items': 3,
            'processed_items': 3,
            'ai_model': 'GPT-4o-mini',
            'processing_time': 45.2,
            'export_date': datetime.now().isoformat()
        }
        
        html_file_with_metadata = generator.generate_with_metadata(excel_file, metadata)
        print(f"✅ Generated with metadata: {html_file_with_metadata}")
        
        # Test package creation
        print("📦 Creating review package...")
        package_dir = "review_package"
        package_files = generator.create_review_package(excel_file, package_dir)
        print(f"✅ Created package in: {package_dir}")
        for key, path in package_files.items():
            print(f"   {key}: {os.path.basename(path)}")
        
        print("\n🎉 All tests passed!")
        print("\n💡 To test the review system:")
        print(f"   1. Open {html_file} in your browser")
        print(f"   2. Select the Excel file: {excel_file}")
        print(f"   3. Review the AI-generated descriptions")
        print(f"   4. Add comments and approval status")
        print(f"   5. Download the updated Excel file")
        
        # Try to open in browser
        try:
            import webbrowser
            webbrowser.open(f"file://{os.path.abspath(html_file)}")
            print(f"\n🌐 Opened {html_file} in browser")
        except Exception as e:
            print(f"\n⚠️ Could not open browser: {e}")
            print(f"   Please manually open: {html_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main test function"""
    success = test_review_page_generation()
    
    if success:
        print("\n✅ Static HTML Review System is working correctly!")
    else:
        print("\n❌ Static HTML Review System test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
