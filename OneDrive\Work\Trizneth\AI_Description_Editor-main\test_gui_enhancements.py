#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test GUI Enhancements
測試 GUI 增強功能
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_new_managers():
    """測試新的管理器"""
    print("🔍 測試新的管理器...")
    
    try:
        from core.settings_manager import SettingsManager
        from core.seo_manager import SEOManager
        from core.i18n import I18n
        
        # 測試設定管理器
        settings = SettingsManager()
        print("✅ 設定管理器載入成功")
        
        # 測試語言設定
        settings.set_language("en_US")
        lang = settings.get_language()
        print(f"✅ 語言設定測試: {lang}")
        
        # 測試 API 金鑰
        settings.update_api_key("openai", "test-key")
        api_key = settings.get_api_key("openai")
        print(f"✅ API 金鑰測試: {api_key[:8]}...")
        
        # 測試 SEO 管理器
        seo_manager = SEOManager()
        print("✅ SEO 管理器載入成功")
        
        # 測試國際化
        i18n = I18n("zh_TW")
        title = i18n.t("main_window_title")
        print(f"✅ 國際化測試 (中文): {title}")
        
        i18n.set_language("en_US")
        title_en = i18n.t("main_window_title")
        print(f"✅ 國際化測試 (英文): {title_en}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dialogs():
    """測試對話框"""
    print("\n🔍 測試對話框類別...")

    try:
        # 只測試導入，不創建實際的 QWidget
        from gui.dialogs import APIKeysDialog, SEOPreviewDialog, ImagePreviewWidget, HTMLPreviewWidget
        from core.i18n import I18n

        print("✅ 對話框類別導入成功")
        print("✅ API 金鑰對話框類別可用")
        print("✅ SEO 預覽對話框類別可用")
        print("✅ 圖片預覽小工具類別可用")
        print("✅ HTML 預覽小工具類別可用")

        return True

    except Exception as e:
        print(f"❌ 對話框測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_imports():
    """測試主視窗導入"""
    print("\n🔍 測試主視窗導入...")
    
    try:
        # 測試是否能正確導入修改後的主視窗
        from config.settings import load_config
        
        config = load_config()
        print("✅ 配置載入成功")
        
        # 不實際創建 GUI，只測試導入
        from gui.main_window import MainWindow
        print("✅ 主視窗類別導入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主視窗導入測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_enhancement_summary():
    """顯示增強功能總結"""
    print("\n💡 GUI 增強功能總結:")
    
    print("\n✨ **新增管理器**:")
    print("1. 📁 SettingsManager - 保存和載入用戶設定")
    print("2. 🔍 SEOManager - 管理 SEO 關鍵字檔案")
    print("3. 🌐 I18n - 多語言支援 (中文/英文)")
    
    print("\n🎨 **UI 增強**:")
    print("1. 🎨 時尚進度條 - 漸層色彩效果")
    print("2. 🔄 重置結果按鈕 - 清除處理結果")
    print("3. 📁 SEO 資料夾選擇 - 支援 SEO 關鍵字")
    print("4. 🔍 SEO 預覽按鈕 - 預覽 SEO 檔案內容")
    
    print("\n📊 **新增對話框**:")
    print("1. 🔑 API 金鑰設定對話框")
    print("2. 🔍 SEO 預覽對話框")
    print("3. 🖼️ 圖片預覽小工具 (含導航)")
    print("4. 📝 HTML 預覽小工具 (含導航)")
    
    print("\n🌐 **多語言支援**:")
    print("• 繁體中文 (zh_TW)")
    print("• 英文 (en_US)")
    print("• 所有 UI 元素都支援多語言")
    
    print("\n💾 **設定記憶功能**:")
    print("• AI 模型選擇")
    print("• Prompt 選擇")
    print("• 欄位設定")
    print("• 檔案路徑")
    print("• 視窗大小和位置")
    print("• API 金鑰")

def main():
    """主測試函數"""
    print("🚀 GUI 增強功能測試")
    print("=" * 60)
    
    tests = [
        ("新管理器", test_new_managers),
        ("對話框類別", test_dialogs),
        ("主視窗導入", test_main_window_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    show_enhancement_summary()
    
    print("\n" + "=" * 60)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有 GUI 增強功能測試通過！")
        print("\n🚀 下一步:")
        print("1. 完成主視窗的剩餘修改")
        print("2. 添加選單列和事件處理")
        print("3. 整合所有新功能")
        print("4. 測試完整的 GUI 功能")
    else:
        print("⚠️ 部分測試失敗，請檢查錯誤訊息。")
    
    return passed == total

if __name__ == "__main__":
    main()
