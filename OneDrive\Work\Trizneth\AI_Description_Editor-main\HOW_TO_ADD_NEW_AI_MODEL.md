# 🤖 如何添加新的 AI 模型 - 完整指南

## 📋 概述

本指南將詳細說明如何在 AI Description Editor 系統中添加新的 AI 模型。無論您使用任何 AI 編程軟體，都可以按照此指南進行操作。

## 🎯 添加新模型的步驟

### **步驟 1: 更新成本計算器定價**

**文件**: `core/cost_calculator.py`

在 `self.pricing` 字典中添加新模型的定價資訊：

```python
# 在 self.pricing 字典中添加新模型
'new-model-key': {
    'input': 0.001,     # 輸入成本 (USD per 1K tokens)
    'output': 0.002,    # 輸出成本 (USD per 1K tokens)
    'display_name': '新模型名稱',
    'features': '✅圖片理解 💰成本等級 🌟精緻度評級',
    'roles': ['Editor', 'Reviewer'],  # 推薦角色
    'free_tier': False,  # 是否有免費額度 (可選)
    'free_limit': 0      # 免費額度限制 (可選)
}
```

**範例**:
```python
'openai-gpt5': {
    'input': 0.008,     # $8.00 per 1M tokens
    'output': 0.024,    # $24.00 per 1M tokens
    'display_name': 'GPT-5',
    'features': '✅超強圖片理解 💰💰💰高成本 🌟🌟🌟🌟🌟🌟精緻度',
    'roles': ['Editor', 'Reviewer']
}
```

### **步驟 2: 更新 AI 模型管理器**

**文件**: `core/ai_models.py`

在 `setup_models()` 方法中添加新模型的初始化：

```python
# 在對應的廠商區塊中添加新模型
new_model = ProviderModel(
    api_key=config['api_key'],
    model_name='actual-api-model-name',  # API 實際使用的模型名稱
    max_tokens=config.get('max_tokens', 2000),
    temperature=config.get('temperature', 0.7)
)
new_model.cost_calculator = self.cost_calculator
self.models['new-model-key'] = new_model
```

**範例**:
```python
# 在 OpenAI 區塊中添加 GPT-5
gpt5_model = OpenAIModel(
    api_key=openai_config['api_key'],
    model_name='gpt-5',  # 實際的 API 模型名稱
    max_tokens=openai_config.get('max_tokens', 2000),
    temperature=openai_config.get('temperature', 0.7)
)
gpt5_model.cost_calculator = self.cost_calculator
self.models['openai-gpt5'] = gpt5_model
```

### **步驟 3: 更新 GUI 模型顯示**

**文件**: `gui/main_window.py`

在 `load_available_options()` 方法的 `model_info` 字典中添加新模型：

```python
'new-model-key': {
    'display': '模型名稱 | 廠商 | 圖片理解 | 成本等級 | 精緻度 | 推薦角色',
    'short': '模型簡稱 (特色)',
    'roles': ['Editor', 'Reviewer']  # 或 ['Editor'] 或 ['Reviewer']
}
```

**範例**:
```python
'openai-gpt5': {
    'display': 'GPT-5 | OpenAI | ✅超強圖片理解 | 💰💰💰高成本 | 🌟🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer',
    'short': 'GPT-5 (次世代)',
    'roles': ['Editor', 'Reviewer']
}
```

### **步驟 4: 更新處理引擎模型映射**

**文件**: `core/processing_engine.py`

在 `_get_model_key()` 方法的 `model_mapping` 字典中添加映射：

```python
# 在 model_mapping 字典中添加
'顯示名稱': 'model-key'
```

**範例**:
```python
'GPT-5': 'openai-gpt5'
```

### **步驟 5: 測試新模型**

創建測試腳本驗證新模型：

```python
#!/usr/bin/env python3
# test_new_model.py

def test_new_model():
    from core.cost_calculator import CostCalculator
    from core.ai_models import AIModelManager
    from config.settings import load_config
    
    # 測試成本計算
    calculator = CostCalculator()
    cost = calculator.calculate_cost('new-model-key', 1000, 500)
    print(f"新模型成本: ${cost:.6f}")
    
    # 測試模型載入
    config = load_config()
    ai_manager = AIModelManager(config, calculator)
    models = ai_manager.get_available_models()
    print(f"可用模型: {models}")
    
    return 'new-model-key' in models

if __name__ == "__main__":
    success = test_new_model()
    print(f"測試結果: {'✅ 成功' if success else '❌ 失敗'}")
```

## 🔧 具體實現範例

### **範例: 添加 Claude 5 模型**

#### **1. 成本計算器** (`core/cost_calculator.py`)
```python
'anthropic-claude5': {
    'input': 0.020,     # $20.00 per 1M tokens
    'output': 0.100,    # $100.00 per 1M tokens
    'display_name': 'Claude 5',
    'features': '✅革命性圖片理解 💰💰💰💰超高成本 🌟🌟🌟🌟🌟🌟精緻度',
    'roles': ['Editor', 'Reviewer']
}
```

#### **2. AI 模型管理器** (`core/ai_models.py`)
```python
# Claude 5 (革命性模型)
claude5_model = AnthropicModel(
    api_key=anthropic_config['api_key'],
    model_name='claude-5-opus-20250101',  # 假設的 API 名稱
    max_tokens=anthropic_config.get('max_tokens', 4000),
    temperature=anthropic_config.get('temperature', 0.7)
)
claude5_model.cost_calculator = self.cost_calculator
self.models['anthropic-claude5'] = claude5_model
```

#### **3. GUI 顯示** (`gui/main_window.py`)
```python
'anthropic-claude5': {
    'display': 'Claude 5 | Anthropic | ✅革命性圖片理解 | 💰💰💰💰超高成本 | 🌟🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer',
    'short': 'Claude 5 (革命性)',
    'roles': ['Editor', 'Reviewer']
}
```

#### **4. 處理引擎映射** (`core/processing_engine.py`)
```python
'Claude 5': 'anthropic-claude5'
```

## 📊 模型特色標記指南

### **圖片理解能力**
- `✅最佳圖片理解` - 業界領先
- `✅精準圖片` - 高精度分析
- `✅強大圖片` - 強大能力
- `✅圖片理解` - 基本圖片理解
- `❌僅文本` - 不支援圖片

### **成本等級**
- `💰` - 經濟 ($0.001-0.002 per 1.5K tokens)
- `💰💰` - 中等 ($0.003-0.010 per 1.5K tokens)
- `💰💰💰` - 高成本 ($0.015-0.050 per 1.5K tokens)
- `💰💰💰💰` - 超高成本 ($0.050+ per 1.5K tokens)

### **精緻度評級**
- `🌟🌟🌟` - 基本品質
- `🌟🌟🌟🌟` - 良好品質
- `🌟🌟🌟🌟🌟` - 優秀品質
- `🌟🌟🌟🌟🌟🌟` - 革命性品質

### **推薦角色**
- `Editor` - 適合內容生成
- `Reviewer` - 適合品質評估
- `Editor+Reviewer` - 兩者皆適合

## 🚨 注意事項

### **1. API 模型名稱**
確保使用正確的 API 模型名稱，這通常與顯示名稱不同：
- 顯示名稱: `GPT-4o mini`
- API 名稱: `gpt-4o-mini`

### **2. 成本計算精確性**
定價必須準確，建議：
- 查閱官方定價頁面
- 使用最新的定價資訊
- 考慮不同地區的定價差異

### **3. 角色推薦邏輯**
根據模型特性推薦角色：
- 高創意模型 → Editor
- 高分析能力模型 → Reviewer
- 平衡模型 → 兩者皆可

### **4. 測試驗證**
添加新模型後務必測試：
- 模型載入是否成功
- 成本計算是否正確
- GUI 顯示是否正常
- 實際 API 呼叫是否工作

## 🔄 更新現有模型

### **更新模型版本**
當廠商發布新版本時：

1. **更新 API 模型名稱**
2. **調整定價** (如有變化)
3. **更新特色描述**
4. **測試新功能**

### **範例: GPT-4o → GPT-4o Turbo**
```python
# 舊版本
'model_name': 'gpt-4o'

# 新版本
'model_name': 'gpt-4o-turbo'
```

## 📝 檢查清單

添加新模型時，請確認：

- [ ] ✅ 成本計算器已更新定價
- [ ] ✅ AI 模型管理器已添加初始化
- [ ] ✅ GUI 已添加模型顯示資訊
- [ ] ✅ 處理引擎已添加模型映射
- [ ] ✅ 測試腳本驗證功能正常
- [ ] ✅ 文檔已更新模型列表
- [ ] ✅ API 金鑰已正確配置
- [ ] ✅ 實際 API 呼叫測試通過

## 🎉 完成

按照此指南，您可以輕鬆添加任何新的 AI 模型到系統中。每次添加新模型後，建議運行完整的測試套件以確保所有功能正常運作。

---

**最後更新**: 2025-01-01  
**適用版本**: AI Description Editor v2.0+
