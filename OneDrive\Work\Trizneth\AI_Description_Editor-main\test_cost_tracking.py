#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Cost Tracking Integration
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cost_tracking():
    """Test cost tracking integration"""
    print("🔍 Testing cost tracking integration...")
    
    try:
        from core.cost_calculator import CostCalculator
        from core.ai_models import AIModelManager
        from config.settings import load_config
        
        # Test cost calculator
        calculator = CostCalculator()
        print("✅ CostCalculator created")
        
        # Test recording usage
        cost1 = calculator.record_usage('openai', 1000, 500)
        cost2 = calculator.record_usage('anthropic', 800, 400)
        cost3 = calculator.record_usage('google', 1200, 600)
        
        print(f"✅ Recorded usage:")
        print(f"   - OpenAI: ${cost1:.6f}")
        print(f"   - Anthropic: ${cost2:.6f}")
        print(f"   - Google: ${cost3:.6f}")
        
        # Test summary
        summary = calculator.get_usage_summary()
        print(f"✅ Usage summary:")
        print(f"   - Total requests: {summary['total_requests']}")
        print(f"   - Total tokens: {summary['total_tokens']:,}")
        print(f"   - Total cost: ${summary['total_cost']:.6f}")
        
        # Test AI model integration
        config = load_config()
        ai_manager = AIModelManager(config, calculator)
        print("✅ AIModelManager with cost calculator created")
        
        # Test GUI integration
        from gui.main_window import MainWindow
        print("✅ MainWindow import successful")
        
        print("\n🎉 Cost tracking integration test passed!")
        print("\n📊 Expected behavior:")
        print("1. ✅ AI models will record token usage automatically")
        print("2. ✅ Cost statistics will update after processing")
        print("3. ✅ Cost Calculator tab will show real-time data")
        print("4. ✅ Processing completion will show total cost")
        
        return True
        
    except Exception as e:
        print(f"❌ Cost tracking test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cost_display():
    """Test cost display formatting"""
    print("\n🔍 Testing cost display formatting...")
    
    try:
        from core.cost_calculator import CostCalculator
        
        calculator = CostCalculator()
        
        # Simulate different usage patterns
        test_cases = [
            ('openai', 1000, 500, "Small request"),
            ('anthropic', 5000, 2000, "Medium request"),
            ('google', 10000, 5000, "Large request"),
            ('openai', 500, 250, "Tiny request"),
        ]
        
        print("📊 Cost breakdown by request:")
        for model, input_tokens, output_tokens, description in test_cases:
            cost = calculator.record_usage(model, input_tokens, output_tokens)
            total_tokens = input_tokens + output_tokens
            print(f"   {description}: {model} - {total_tokens:,} tokens = ${cost:.6f}")
        
        # Show final summary
        summary = calculator.get_usage_summary()
        breakdown = calculator.get_cost_breakdown()
        
        print(f"\n💰 Final Summary:")
        print(f"   Total Requests: {summary['total_requests']}")
        print(f"   Total Tokens: {summary['total_tokens']:,}")
        print(f"   Total Cost: ${summary['total_cost']:.6f}")
        
        print(f"\n📈 By Model:")
        for item in breakdown:
            print(f"   {item['display_name']}: {item['requests']} requests, ${item['cost']:.6f}")
        
        print("✅ Cost display formatting test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Cost display test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Cost Tracking Integration Test")
    print("=" * 50)
    
    tests = [
        ("Cost Tracking Integration", test_cost_tracking),
        ("Cost Display Formatting", test_cost_display),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Cost Tracking Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All cost tracking tests passed!")
        print("\n🚀 Now when you run the program:")
        print("1. Load Excel data and start processing")
        print("2. Check the 'Cost Calculation' tab for real-time usage")
        print("3. See cost information in the log after processing")
        print("4. Monitor spending across different AI models")
    else:
        print("⚠️ Some cost tracking tests failed.")
    
    return passed == total

if __name__ == "__main__":
    main()
