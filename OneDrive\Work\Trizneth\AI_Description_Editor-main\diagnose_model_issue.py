#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnose Model Issue
診斷模型問題
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def diagnose_model_issue():
    """診斷模型問題"""
    print("🔍 診斷模型問題...")
    
    try:
        # 不啟動 GUI，只測試核心功能
        from core.cost_calculator import CostCalculator
        from config.settings import load_config
        
        # 測試成本計算器
        calculator = CostCalculator()
        print("✅ 成本計算器已載入")
        
        print("\n📋 可用的模型鍵值:")
        for model_key in calculator.pricing.keys():
            print(f"   - {model_key}")
        
        # 測試配置載入
        config = load_config()
        print("✅ 配置已載入")
        
        # 測試 AI 模型管理器（不啟動 GUI）
        from core.ai_models import AIModelManager
        ai_manager = AIModelManager(config, calculator)
        print("✅ AI 模型管理器已載入")
        
        available_models = ai_manager.get_available_models()
        print(f"\n📊 AI 管理器中的可用模型:")
        for model in available_models:
            print(f"   - {model}")
        
        # 檢查模型映射
        print(f"\n🔧 檢查模型映射:")
        
        # 模型顯示名稱到鍵值的映射
        model_mapping = {
            'GPT-4o': 'openai-gpt4o',
            'GPT-4o mini': 'openai-gpt4o-mini',
            'Claude Opus 4': 'anthropic-opus4',
            'Claude Sonnet 4': 'anthropic-sonnet4',
            'Gemini 2.5 Pro': 'google-pro25',
            'Gemini 2.5 Flash': 'google-flash25'
        }
        
        for display_name, expected_key in model_mapping.items():
            is_in_pricing = expected_key in calculator.pricing
            is_in_ai_manager = expected_key in available_models
            
            status_pricing = "✅" if is_in_pricing else "❌"
            status_ai = "✅" if is_in_ai_manager else "❌"
            
            print(f"   {display_name}:")
            print(f"     鍵值: {expected_key}")
            print(f"     定價表: {status_pricing}")
            print(f"     AI管理器: {status_ai}")
        
        # 檢查是否有舊的模型鍵值
        old_keys = ['anthropic-sonnet', 'anthropic-opus', 'anthropic-haiku', 'openai-gpt35', 'google-pro', 'google-flash']
        print(f"\n🔍 檢查舊模型鍵值:")
        for old_key in old_keys:
            in_pricing = old_key in calculator.pricing
            in_ai_manager = old_key in available_models
            
            if in_pricing or in_ai_manager:
                print(f"   ⚠️ 發現舊鍵值: {old_key} (定價:{in_pricing}, AI管理器:{in_ai_manager})")
            else:
                print(f"   ✅ 已清理: {old_key}")
        
        print("\n🎉 診斷完成！")
        return True
        
    except Exception as e:
        print(f"❌ 診斷失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_suggestions():
    """顯示修復建議"""
    print("\n🔧 修復建議:")
    
    print("\n1. **確認模型鍵值一致性**:")
    print("   - 成本計算器中的模型鍵值")
    print("   - AI 模型管理器中的模型鍵值")
    print("   - GUI 中的模型映射")
    print("   - 處理引擎中的模型映射")
    
    print("\n2. **檢查 API 金鑰**:")
    print("   - 確保 Anthropic API 金鑰已正確設定")
    print("   - 檢查 config/ai_keys.env 文件")
    
    print("\n3. **重新啟動程式**:")
    print("   - 完全關閉程式")
    print("   - 重新啟動: python main.py")
    
    print("\n4. **測試單一模型**:")
    print("   - 先選擇 GPT-4o mini (最經濟)")
    print("   - 測試是否能正常處理")
    print("   - 再嘗試其他模型")

def main():
    """主診斷函數"""
    print("🚀 模型問題診斷工具")
    print("=" * 50)
    
    success = diagnose_model_issue()
    show_fix_suggestions()
    
    if success:
        print("\n✅ 診斷完成！")
        print("\n🎯 關鍵檢查點:")
        print("1. 確認所有新模型鍵值都存在於定價表和 AI 管理器中")
        print("2. 確認沒有舊的模型鍵值殘留")
        print("3. 確認 API 金鑰配置正確")
        
        print("\n🚀 下一步:")
        print("1. 如果所有檢查都通過，重新啟動程式")
        print("2. 選擇 GPT-4o mini 進行測試")
        print("3. 如果仍有問題，檢查 API 金鑰")
    else:
        print("\n❌ 診斷過程中遇到問題。")
    
    return success

if __name__ == "__main__":
    main()
