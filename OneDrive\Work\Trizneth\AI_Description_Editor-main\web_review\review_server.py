#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Description Review System - Backend Server
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import logging

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReviewServer:
    """Web-based review system server"""
    
    def __init__(self, port: int = 8080):
        self.app = Flask(__name__)
        CORS(self.app)  # Enable CORS for frontend
        
        self.port = port
        self.excel_file = None
        self.excel_data = None
        self.reviews_file = None
        self.reviews_data = {}
        
        self.setup_routes()
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Serve the main review page"""
            return send_from_directory('.', 'review_template.html')
        
        @self.app.route('/review_system.js')
        def serve_js():
            """Serve the JavaScript file"""
            return send_from_directory('.', 'review_system.js')
        
        @self.app.route('/api/load-excel', methods=['POST'])
        def load_excel():
            """Load Excel file and extract HTML content"""
            try:
                data = request.get_json()
                excel_path = data.get('excel_path')
                
                if not excel_path or not os.path.exists(excel_path):
                    return jsonify({'success': False, 'error': 'Excel file not found'})
                
                result = self.load_excel_data(excel_path)
                return jsonify(result)
                
            except Exception as e:
                logger.error(f"Error loading Excel: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/load-excel', methods=['GET'])
        def load_excel_get():
            """Load Excel file from default location"""
            try:
                # Look for Excel files in current directory
                excel_files = list(Path('.').glob('*.xlsx'))
                if not excel_files:
                    return jsonify({'success': False, 'error': 'No Excel files found in current directory'})
                
                # Use the first Excel file found
                excel_path = str(excel_files[0])
                result = self.load_excel_data(excel_path)
                return jsonify(result)
                
            except Exception as e:
                logger.error(f"Error loading Excel: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/save-review', methods=['POST'])
        def save_review():
            """Save review comment back to Excel"""
            try:
                data = request.get_json()
                index = data.get('index')
                review = data.get('review')
                
                result = self.save_review_data(index, review)
                return jsonify(result)
                
            except Exception as e:
                logger.error(f"Error saving review: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/export-reviews', methods=['POST'])
        def export_reviews():
            """Export all reviews to Excel"""
            try:
                result = self.export_all_reviews()
                return jsonify(result)
                
            except Exception as e:
                logger.error(f"Error exporting reviews: {e}")
                return jsonify({'success': False, 'error': str(e)})
    
    def load_excel_data(self, excel_path: str) -> Dict[str, Any]:
        """Load Excel data and extract HTML content"""
        try:
            self.excel_file = excel_path
            self.excel_data = pd.read_excel(excel_path)
            
            # Setup reviews file
            excel_name = Path(excel_path).stem
            self.reviews_file = f"{excel_name}_reviews.json"
            self.load_existing_reviews()
            
            # Extract items with HTML content
            items = []
            html_columns = [col for col in self.excel_data.columns if 'html' in col.lower() or 'output' in col.lower()]
            
            if not html_columns:
                return {'success': False, 'error': 'No HTML columns found in Excel file'}
            
            html_column = html_columns[0]  # Use first HTML column found
            
            for index, row in self.excel_data.iterrows():
                html_content = row.get(html_column, '')
                if pd.notna(html_content) and html_content.strip():
                    items.append({
                        'rowIndex': index,
                        'productName': row.get('Product Name', row.get('Title', f'Item {index + 1}')),
                        'category': row.get('Category', 'General'),
                        'htmlContent': str(html_content),
                        'originalData': row.to_dict()
                    })
            
            logger.info(f"Loaded {len(items)} items from {excel_path}")
            
            return {
                'success': True,
                'items': items,
                'reviews': self.reviews_data,
                'excel_file': excel_path,
                'html_column': html_column
            }
            
        except Exception as e:
            logger.error(f"Error loading Excel data: {e}")
            return {'success': False, 'error': str(e)}
    
    def load_existing_reviews(self):
        """Load existing reviews from JSON file"""
        try:
            if os.path.exists(self.reviews_file):
                with open(self.reviews_file, 'r', encoding='utf-8') as f:
                    self.reviews_data = json.load(f)
                logger.info(f"Loaded existing reviews from {self.reviews_file}")
            else:
                self.reviews_data = {}
        except Exception as e:
            logger.error(f"Error loading reviews: {e}")
            self.reviews_data = {}
    
    def save_review_data(self, index: int, review: Dict[str, Any]) -> Dict[str, Any]:
        """Save review data"""
        try:
            # Save to memory
            self.reviews_data[str(index)] = {
                **review,
                'timestamp': datetime.now().isoformat(),
                'row_index': index
            }
            
            # Save to JSON file
            with open(self.reviews_file, 'w', encoding='utf-8') as f:
                json.dump(self.reviews_data, f, indent=2, ensure_ascii=False)
            
            # Save to Excel if possible
            self.update_excel_with_review(index, review)
            
            logger.info(f"Saved review for item {index}")
            return {'success': True, 'message': 'Review saved successfully'}
            
        except Exception as e:
            logger.error(f"Error saving review: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_excel_with_review(self, index: int, review: Dict[str, Any]):
        """Update Excel file with review data"""
        try:
            if self.excel_data is None:
                return
            
            # Add review columns if they don't exist
            if 'Review Status' not in self.excel_data.columns:
                self.excel_data['Review Status'] = ''
            if 'Review Comments' not in self.excel_data.columns:
                self.excel_data['Review Comments'] = ''
            if 'Review Date' not in self.excel_data.columns:
                self.excel_data['Review Date'] = ''
            
            # Update the specific row
            if index < len(self.excel_data):
                self.excel_data.loc[index, 'Review Status'] = review.get('status', 'pending')
                self.excel_data.loc[index, 'Review Comments'] = review.get('comment', '')
                self.excel_data.loc[index, 'Review Date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # Save back to Excel
                self.excel_data.to_excel(self.excel_file, index=False)
                logger.info(f"Updated Excel file with review for row {index}")
                
        except Exception as e:
            logger.error(f"Error updating Excel: {e}")
    
    def export_all_reviews(self) -> Dict[str, Any]:
        """Export all reviews to a summary file"""
        try:
            if not self.reviews_data:
                return {'success': False, 'error': 'No reviews to export'}
            
            # Create summary data
            summary_data = []
            for index_str, review in self.reviews_data.items():
                index = int(index_str)
                if self.excel_data is not None and index < len(self.excel_data):
                    row = self.excel_data.iloc[index]
                    summary_data.append({
                        'Row': index + 1,
                        'Product Name': row.get('Product Name', f'Item {index + 1}'),
                        'Review Status': review.get('status', 'pending'),
                        'Review Comments': review.get('comment', ''),
                        'Review Date': review.get('timestamp', ''),
                        'Category': row.get('Category', 'General')
                    })
            
            # Save to Excel
            summary_df = pd.DataFrame(summary_data)
            summary_file = f"review_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            summary_df.to_excel(summary_file, index=False)
            
            logger.info(f"Exported {len(summary_data)} reviews to {summary_file}")
            
            return {
                'success': True,
                'message': f'Reviews exported to {summary_file}',
                'file': summary_file,
                'count': len(summary_data)
            }
            
        except Exception as e:
            logger.error(f"Error exporting reviews: {e}")
            return {'success': False, 'error': str(e)}
    
    def run(self, debug: bool = False):
        """Run the Flask server"""
        logger.info(f"Starting review server on http://localhost:{self.port}")
        logger.info("Open your browser and navigate to the URL above")
        self.app.run(host='0.0.0.0', port=self.port, debug=debug)

def main():
    """Main function to start the review server"""
    import argparse
    
    parser = argparse.ArgumentParser(description='AI Description Review System')
    parser.add_argument('--port', type=int, default=8080, help='Server port (default: 8080)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--excel', type=str, help='Path to Excel file to review')
    
    args = parser.parse_args()
    
    # Create and run server
    server = ReviewServer(port=args.port)
    
    print("🌐 AI Description Review System")
    print("=" * 50)
    print(f"🚀 Server starting on http://localhost:{args.port}")
    print("📁 Place your Excel file in the current directory")
    print("🔍 The system will automatically detect Excel files with HTML content")
    print("💾 Reviews will be saved back to the Excel file")
    print("⌨️  Keyboard shortcuts: Ctrl+← (Previous), Ctrl+→ (Next), Ctrl+S (Save)")
    print("=" * 50)
    
    try:
        server.run(debug=args.debug)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == '__main__':
    main()
