#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test furniture prompt with sample data to verify English-only output
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_furniture_prompt():
    """Test furniture prompt with sample data"""
    print("🧪 Testing Furniture Prompt")
    print("=" * 50)
    
    # Sample test data with mixed languages (like your real data)
    test_cases = [
        {
            "name": "Vista Dining Chair",
            "data": {
                "Product Name": "Vista Dining Chair",
                "Material": "白橡木",  # Chinese
                "Dimensions": "54*62*80",
                "Color": "Natural Wood"
            }
        },
        {
            "name": "Arta Armchair", 
            "data": {
                "Product Name": "Arta Armchair",
                "Dimensions": "长 60*宽 50*高 84*座高 50*扶手高 67cm",  # Chinese
                "Material": "皮+白橡木椅子",  # Chinese
                "Weight": "15kg"
            }
        },
        {
            "name": "Modern Office Chair",
            "data": {
                "Product Name": "Modern Office Chair", 
                "坐宽": "56",  # Chinese headers
                "坐高": "47",
                "靠高": "80", 
                "坐深": "46",
                "Material": "米色皮 绒布色白橡木椅子"  # Chinese
            }
        },
        {
            "name": "Simple Stool",
            "data": {
                "Product Name": "Simple Stool"
                # No specifications - should hide table
            }
        }
    ]
    
    try:
        from core.prompt_manager import PromptManager
        from config.settings import load_config
        
        config = load_config()
        prompt_manager = PromptManager(config.get('paths.prompts_dir'))
        
        print("✅ Prompt manager loaded")
        
        # Test each case
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test Case {i}: {test_case['name']}")
            print("-" * 40)
            
            # Format the prompt
            try:
                formatted_prompt = prompt_manager.format_prompt(
                    "furniture",
                    product_name=test_case['name'],
                    product_data=test_case['data']
                )
                
                print("✅ Prompt formatted successfully")
                
                # Check for key requirements
                checks = {
                    "Contains English language rule": "ALL content must be written in English only" in formatted_prompt,
                    "Contains table rules": "CRITICAL TABLE RULES" in formatted_prompt,
                    "Contains translation examples": "白橡木 = White Oak" in formatted_prompt,
                    "Contains dimension format": "54×62×80 cm" in formatted_prompt,
                    "Contains final reminder": "FINAL REMINDER" in formatted_prompt
                }
                
                print("🔍 Prompt Content Checks:")
                for check, passed in checks.items():
                    print(f"   {'✅' if passed else '❌'} {check}")
                
                # Show a snippet of the formatted prompt
                print(f"\n📄 Prompt Preview (first 200 chars):")
                print(f"   {formatted_prompt[:200]}...")
                
            except Exception as e:
                print(f"❌ Failed to format prompt: {e}")
    
    except Exception as e:
        print(f"❌ Failed to load prompt manager: {e}")
        return
    
    print(f"\n💡 Expected Improvements:")
    print(f"   ✅ All Chinese text should be translated to English")
    print(f"   ✅ Dimensions should use consistent format (54×62×80 cm)")
    print(f"   ✅ Table should be hidden when no specifications available")
    print(f"   ✅ Text should have proper line breaks for readability")
    print(f"   ✅ All content should be in English only")
    
    print(f"\n🎯 Test the updated prompt by:")
    print(f"   1. Run the application")
    print(f"   2. Select 'furniture' prompt")
    print(f"   3. Process a furniture item with Chinese text")
    print(f"   4. Verify output is all in English")
    print(f"   5. Check table formatting and dimension consistency")

if __name__ == "__main__":
    test_furniture_prompt()
