#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to test image processing
"""

import sys
import os
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_processor import ImageProcessor

def debug_image_processing():
    """Debug image processing step by step"""
    print("🔍 Image Processing Debug Tool")
    print("=" * 50)
    
    # Get image directory from user
    image_dir = input("Enter your image directory path: ").strip()
    if not image_dir:
        print("❌ No directory provided")
        return
    
    if not os.path.exists(image_dir):
        print(f"❌ Directory does not exist: {image_dir}")
        return
    
    print(f"📁 Image directory: {image_dir}")
    
    # Initialize image processor
    try:
        processor = ImageProcessor(image_dir)
        print("✅ Image processor initialized")
    except Exception as e:
        print(f"❌ Failed to initialize image processor: {e}")
        return
    
    # Validate directory
    validation = processor.validate_images_directory()
    print(f"\n📊 Directory validation:")
    print(f"   Valid: {validation['valid']}")
    print(f"   Directory exists: {validation['directory_exists']}")
    print(f"   Total images: {validation['total_images']}")
    print(f"   Supported formats: {validation['supported_formats']}")
    if validation['warnings']:
        print(f"   Warnings: {validation['warnings']}")
    
    if not validation['valid']:
        print("❌ Image directory validation failed")
        return
    
    # Test image search
    print(f"\n🔍 Testing image search...")
    test_filename = input("Enter a test filename (from your Excel): ").strip()
    if test_filename:
        found_images = processor.find_images(test_filename)
        print(f"   Searching for: '{test_filename}'")
        print(f"   Found {len(found_images)} images:")
        for img in found_images:
            print(f"     - {img}")
            
        if found_images:
            print("✅ Image search working correctly!")
            
            # Test image info
            first_image = found_images[0]
            info = processor.get_image_info(first_image)
            print(f"\n📷 Image info for {os.path.basename(first_image)}:")
            for key, value in info.items():
                print(f"   {key}: {value}")
        else:
            print("❌ No images found. Check:")
            print("   1. Filename matches exactly (without extension)")
            print("   2. Image files exist in the directory")
            print("   3. File extensions are supported (.jpg, .png, .webp, etc.)")
    
    print(f"\n🎯 Debug Summary:")
    print(f"   Image directory: {'✅ Valid' if validation['valid'] else '❌ Invalid'}")
    print(f"   Total images: {validation['total_images']}")
    print(f"   Image search: {'✅ Working' if found_images else '❌ No results'}")
    
    print(f"\n💡 Tips for GPT-4o mini image processing:")
    print(f"   1. ✅ Enable 'Image Assistance' checkbox")
    print(f"   2. ✅ Select this image directory: {image_dir}")
    print(f"   3. ✅ Select image column in Excel settings")
    print(f"   4. ✅ Choose GPT-4o mini (shows ✅圖片理解)")
    print(f"   5. ✅ Make sure filenames in Excel match image files")

if __name__ == "__main__":
    debug_image_processing()
