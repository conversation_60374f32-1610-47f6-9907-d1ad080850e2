#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Settings for AI Description Editor
AI 商品描述編輯器配置設定
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class Config:
    """配置管理類別"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.load_environment()
        self.load_default_settings()
    
    def load_environment(self):
        """載入環境變數"""
        env_file = self.project_root / "config" / "ai_keys.env"
        if env_file.exists():
            load_dotenv(env_file)
    
    def load_default_settings(self):
        """載入預設設定"""
        self.settings = {
            # AI 模型設定
            "ai_models": {
                "openai": {
                    "api_key": os.getenv("OPENAI_API_KEY", ""),
                    "model": "gpt-4",
                    "max_tokens": 2000,
                    "temperature": 0.7
                },
                "anthropic": {
                    "api_key": os.getenv("ANTHROPIC_API_KEY", ""),
                    "model": "claude-3-5-sonnet-20241022",
                    "max_tokens": 2000,
                    "temperature": 0.7
                },
                "google": {
                    "api_key": os.getenv("GOOGLE_API_KEY", ""),
                    "model": "gemini-pro",
                    "max_tokens": 2000,
                    "temperature": 0.7
                }
            },
            
            # 檔案路徑設定
            "paths": {
                "prompts_dir": str(self.project_root / "prompts"),
                "categories_dir": str(self.project_root / "categories"),
                "pictures_dir": str(self.project_root / "Product Picture"),
                "logs_dir": str(self.project_root / "logs"),
                "output_dir": str(self.project_root / "output")
            },
            
            # HTML 輸出設定
            "html_template": {
                "structure": [
                    "h1:產品名稱",
                    "h2:Description",
                    "h3:Product Overview",
                    "h3:Main Benefits",
                    "h2:Ingredients",
                    "h3:Active Ingredients",
                    "h3:Free From / Allergy Info",
                    "h2:How to Use",
                    "h3:Dosage",
                    "h3:Usage Warnings",
                    "h2:Additional Information",
                    "h3:Miscellaneous"
                ]
            },
            
            # GUI 設定
            "gui": {
                "window_size": [1200, 800],
                "preview_size": [600, 400],
                "font_family": "Microsoft YaHei",
                "font_size": 10
            },
            
            # 處理設定
            "processing": {
                "batch_size": 10,
                "retry_attempts": 3,
                "timeout_seconds": 30,
                "enable_image_analysis": True,
                "max_keywords_per_description": 5
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """取得設定值"""
        keys = key.split('.')
        value = self.settings
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any):
        """設定值"""
        keys = key.split('.')
        target = self.settings
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]
        target[keys[-1]] = value
    
    def save_to_file(self, filepath: Optional[str] = None):
        """儲存設定到檔案"""
        if filepath is None:
            filepath = self.project_root / "config" / "settings.yaml"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            yaml.dump(self.settings, f, default_flow_style=False, allow_unicode=True)
    
    def load_from_file(self, filepath: str):
        """從檔案載入設定"""
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                loaded_settings = yaml.safe_load(f)
                if loaded_settings:
                    self.settings.update(loaded_settings)


def load_config() -> Config:
    """載入配置"""
    config = Config()
    
    # 嘗試載入自訂設定檔
    settings_file = config.project_root / "config" / "settings.yaml"
    if settings_file.exists():
        config.load_from_file(str(settings_file))
    
    return config
