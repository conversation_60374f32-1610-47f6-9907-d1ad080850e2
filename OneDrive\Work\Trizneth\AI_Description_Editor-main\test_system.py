#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for AI Description Editor
AI 商品描述編輯器測試腳本
"""

import sys
import os
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """測試所有模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        # 測試配置模組
        from config.settings import load_config
        print("✅ 配置模組導入成功")
        
        # 測試核心模組
        from core.data_processor import ExcelProcessor, ImageProcessor
        from core.ai_models import AIModelManager
        from core.prompt_manager import PromptManager
        from core.keyword_manager import KeywordManager
        from core.html_generator import HTMLGenerator
        from core.processing_engine import ProcessingEngine
        from core.review_engine import ReviewEngine
        print("✅ 核心模組導入成功")
        
        # 測試工具模組
        from utils.logger import setup_logger
        print("✅ 工具模組導入成功")
        
        # 測試 GUI 模組
        from gui.main_window import MainWindow
        print("✅ GUI 模組導入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模組導入失敗: {e}")
        return False


def test_configuration():
    """測試配置載入"""
    print("\n🔍 測試配置載入...")
    
    try:
        from config.settings import load_config
        config = load_config()
        
        print(f"✅ 配置載入成功")
        print(f"   - AI 模型數量: {len(config.get('ai_models', {}))}")
        print(f"   - 路徑設定: {len(config.get('paths', {}))}")
        print(f"   - HTML 模板: {len(config.get('html_template.structure', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置載入失敗: {e}")
        return False


def test_data_processing():
    """測試資料處理功能"""
    print("\n🔍 測試資料處理...")
    
    try:
        from core.data_processor import ExcelProcessor
        
        # 檢查範例檔案
        sample_file = project_root / "sample_products.xlsx"
        if not sample_file.exists():
            print("⚠️ 範例 Excel 檔案不存在，跳過資料處理測試")
            return True
        
        processor = ExcelProcessor()
        success = processor.load_excel(str(sample_file))
        
        if success:
            validation = processor.validate_data()
            print(f"✅ Excel 處理成功")
            print(f"   - 總行數: {validation['total_rows']}")
            print(f"   - 總欄數: {validation['total_columns']}")
            print(f"   - 驗證狀態: {'通過' if validation['valid'] else '失敗'}")
        else:
            print("❌ Excel 處理失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 資料處理測試失敗: {e}")
        return False


def test_keyword_management():
    """測試關鍵字管理"""
    print("\n🔍 測試關鍵字管理...")
    
    try:
        from core.keyword_manager import KeywordManager
        
        manager = KeywordManager(str(project_root / "categories"))
        stats = manager.get_keyword_stats()
        
        print(f"✅ 關鍵字管理成功")
        print(f"   - 通用關鍵字: {stats['general_keywords']}")
        print(f"   - 分類數量: {stats['categories']}")
        print(f"   - 總分類關鍵字: {stats['total_category_keywords']}")
        
        # 測試關鍵字選擇
        test_data = {"Product Name": "維生素C", "Category": "Immunity"}
        keywords = manager.get_keywords_for_product(test_data, "Immunity", 5)
        print(f"   - 測試關鍵字選擇: {len(keywords)} 個關鍵字")
        
        return True
        
    except Exception as e:
        print(f"❌ 關鍵字管理測試失敗: {e}")
        return False


def test_prompt_management():
    """測試 Prompt 管理"""
    print("\n🔍 測試 Prompt 管理...")
    
    try:
        from core.prompt_manager import PromptManager
        
        manager = PromptManager(str(project_root / "prompts"))
        
        writer_prompts = manager.get_writer_prompts()
        reviewer_prompts = manager.get_reviewer_prompts()
        
        print(f"✅ Prompt 管理成功")
        print(f"   - Writer Prompts: {len(writer_prompts)}")
        print(f"   - Reviewer Prompts: {len(reviewer_prompts)}")
        
        if writer_prompts:
            print(f"   - 可用 Writer: {', '.join(writer_prompts)}")
        if reviewer_prompts:
            print(f"   - 可用 Reviewer: {', '.join(reviewer_prompts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Prompt 管理測試失敗: {e}")
        return False


def test_html_generation():
    """測試 HTML 生成"""
    print("\n🔍 測試 HTML 生成...")
    
    try:
        from core.html_generator import HTMLGenerator
        
        generator = HTMLGenerator()
        
        # 測試 HTML 結構
        structure = generator.get_template_structure()
        print(f"✅ HTML 生成器成功")
        print(f"   - 模板結構: {len(structure)} 個元素")
        
        # 測試 HTML 驗證
        test_html = """
        <h1>測試產品</h1>
        <h2>Description</h2>
        <h3>Product Overview</h3>
        <p>這是一個測試產品</p>
        """
        
        validation = generator.validate_html_structure(test_html)
        print(f"   - HTML 驗證: {'通過' if validation['valid'] else '需要改善'}")
        
        return True
        
    except Exception as e:
        print(f"❌ HTML 生成測試失敗: {e}")
        return False


def test_logging():
    """測試日誌系統"""
    print("\n🔍 測試日誌系統...")
    
    try:
        from utils.logger import setup_logger
        
        logger = setup_logger()
        logger.info("測試日誌訊息")
        
        # 檢查日誌目錄
        logs_dir = project_root / "logs"
        if logs_dir.exists():
            log_files = list(logs_dir.glob("*.log"))
            print(f"✅ 日誌系統成功")
            print(f"   - 日誌檔案: {len(log_files)} 個")
        else:
            print("✅ 日誌系統成功（目錄將在首次使用時建立）")
        
        return True
        
    except Exception as e:
        print(f"❌ 日誌系統測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🧠 AI 商品描述優化系統 - 系統測試")
    print("=" * 50)
    
    tests = [
        ("模組導入", test_imports),
        ("配置載入", test_configuration),
        ("資料處理", test_data_processing),
        ("關鍵字管理", test_keyword_management),
        ("Prompt 管理", test_prompt_management),
        ("HTML 生成", test_html_generation),
        ("日誌系統", test_logging),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！系統準備就緒。")
        print("\n📋 下一步:")
        print("1. 設定 API 金鑰在 config/ai_keys.env")
        print("2. 執行 python main.py 啟動 GUI")
        print("3. 載入 sample_products.xlsx 進行測試")
    else:
        print("⚠️ 部分測試失敗，請檢查錯誤訊息。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
