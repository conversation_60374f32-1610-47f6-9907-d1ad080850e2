#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Review Page Generator - Creates static HTML review pages alongside Excel exports
"""

import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class ReviewPageGenerator:
    """Generates static HTML review pages for Excel exports"""
    
    def __init__(self):
        self.template_path = Path(__file__).parent.parent / "templates" / "review_page_template.html"
        
    def generate_review_page(
        self, 
        excel_file_path: str, 
        output_dir: Optional[str] = None,
        custom_title: Optional[str] = None
    ) -> str:
        """
        Generate a static HTML review page alongside the Excel export
        
        Args:
            excel_file_path: Path to the exported Excel file
            output_dir: Directory to save the HTML file (defaults to same as Excel)
            custom_title: Custom title for the review page
            
        Returns:
            str: Path to the generated HTML file
        """
        try:
            excel_path = Path(excel_file_path)
            
            # Determine output directory
            if output_dir:
                output_path = Path(output_dir)
            else:
                output_path = excel_path.parent
            
            # Ensure output directory exists
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Generate HTML filename
            excel_name = excel_path.stem
            html_filename = f"{excel_name}_review.html"
            html_file_path = output_path / html_filename
            
            # Load template
            if not self.template_path.exists():
                raise FileNotFoundError(f"Template not found: {self.template_path}")
            
            with open(self.template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # Replace template variables
            export_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            page_title = custom_title or f"AI Description Review - {excel_name}"
            
            html_content = template_content.replace('{export_date}', export_date)
            html_content = html_content.replace(
                '<title>AI Description Review - {export_date}</title>',
                f'<title>{page_title}</title>'
            )
            
            # Add Excel file information to the page
            excel_info_script = f"""
            <script>
                // Auto-load Excel file information
                window.addEventListener('DOMContentLoaded', function() {{
                    const excelFileName = '{excel_path.name}';
                    const exportDate = '{export_date}';
                    
                    // Update header with file info
                    const headerTitle = document.querySelector('.header h1');
                    if (headerTitle) {{
                        headerTitle.innerHTML = '🔍 AI Description Review<br><small style="font-size: 14px; opacity: 0.8;">File: ' + excelFileName + ' | Exported: ' + exportDate + '</small>';
                    }}
                    
                    // Update welcome screen
                    const welcomeScreen = document.getElementById('welcomeScreen');
                    if (welcomeScreen) {{
                        const fileInfo = document.createElement('div');
                        fileInfo.style.cssText = 'background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;';
                        fileInfo.innerHTML = `
                            <h4 style="color: #1976d2; margin-bottom: 8px;">📁 Expected Excel File</h4>
                            <p style="color: #666; margin: 0;"><strong>${{excelFileName}}</strong></p>
                            <p style="color: #666; margin: 5px 0 0 0; font-size: 12px;">Generated: ${{exportDate}}</p>
                        `;
                        welcomeScreen.insertBefore(fileInfo, welcomeScreen.querySelector('.upload-area'));
                    }}
                }});
            </script>
            """
            
            # Insert the script before closing body tag
            html_content = html_content.replace('</body>', f'{excel_info_script}</body>')
            
            # Write HTML file
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Generated review page: {html_file_path}")
            return str(html_file_path)
            
        except Exception as e:
            logger.error(f"Failed to generate review page: {e}")
            raise
    
    def generate_with_metadata(
        self,
        excel_file_path: str,
        metadata: Dict[str, Any],
        output_dir: Optional[str] = None
    ) -> str:
        """
        Generate review page with additional metadata
        
        Args:
            excel_file_path: Path to the exported Excel file
            metadata: Additional metadata to include
            output_dir: Directory to save the HTML file
            
        Returns:
            str: Path to the generated HTML file
        """
        try:
            # Extract metadata
            total_items = metadata.get('total_items', 0)
            processed_items = metadata.get('processed_items', 0)
            ai_model = metadata.get('ai_model', 'Unknown')
            processing_time = metadata.get('processing_time', 0)
            
            # Generate custom title
            custom_title = f"AI Review - {processed_items}/{total_items} items ({ai_model})"
            
            # Generate base HTML
            html_file_path = self.generate_review_page(
                excel_file_path, 
                output_dir, 
                custom_title
            )
            
            # Add metadata script
            metadata_script = f"""
            <script>
                // Processing metadata
                window.processingMetadata = {{
                    totalItems: {total_items},
                    processedItems: {processed_items},
                    aiModel: '{ai_model}',
                    processingTime: {processing_time},
                    exportDate: '{datetime.now().isoformat()}'
                }};
                
                // Update interface with metadata
                window.addEventListener('DOMContentLoaded', function() {{
                    const metadata = window.processingMetadata;
                    
                    // Add metadata to welcome screen
                    const welcomeScreen = document.getElementById('welcomeScreen');
                    if (welcomeScreen) {{
                        const metadataDiv = document.createElement('div');
                        metadataDiv.style.cssText = 'background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;';
                        metadataDiv.innerHTML = `
                            <h4 style="color: #495057; margin-bottom: 10px;">📊 Processing Summary</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                                <div><strong>Items Processed:</strong> ${{metadata.processedItems}}/${{metadata.totalItems}}</div>
                                <div><strong>AI Model:</strong> ${{metadata.aiModel}}</div>
                                <div><strong>Processing Time:</strong> ${{Math.round(metadata.processingTime)}}s</div>
                                <div><strong>Export Date:</strong> ${{new Date(metadata.exportDate).toLocaleString()}}</div>
                            </div>
                        `;
                        welcomeScreen.appendChild(metadataDiv);
                    }}
                }});
            </script>
            """
            
            # Append metadata script to HTML file
            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            html_content = html_content.replace('</body>', f'{metadata_script}</body>')
            
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Added metadata to review page: {html_file_path}")
            return html_file_path
            
        except Exception as e:
            logger.error(f"Failed to generate review page with metadata: {e}")
            raise
    
    def create_review_package(
        self,
        excel_file_path: str,
        output_dir: str,
        include_instructions: bool = True
    ) -> Dict[str, str]:
        """
        Create a complete review package with HTML file and instructions
        
        Args:
            excel_file_path: Path to the exported Excel file
            output_dir: Directory to save the package
            include_instructions: Whether to include instruction files
            
        Returns:
            Dict[str, str]: Paths to generated files
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Copy Excel file to output directory
            excel_path = Path(excel_file_path)
            excel_copy_path = output_path / excel_path.name
            shutil.copy2(excel_file_path, excel_copy_path)
            
            # Generate HTML review page
            html_file_path = self.generate_review_page(str(excel_copy_path), output_dir)
            
            result = {
                'html_file': html_file_path,
                'excel_file': str(excel_copy_path)
            }
            
            # Create instructions file if requested
            if include_instructions:
                instructions_path = self.create_instructions_file(output_path)
                result['instructions_file'] = instructions_path
            
            logger.info(f"Created review package in: {output_dir}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to create review package: {e}")
            raise
    
    def create_instructions_file(self, output_dir: Path) -> str:
        """Create an instructions file for the review system"""
        instructions_content = """# 🔍 AI Description Review System - Instructions

## 📋 How to Use This Review System

### 1. Getting Started
1. **Open the HTML file** in your web browser (Chrome, Firefox, Safari, Edge)
2. **Select the Excel file** using the file picker or drag & drop
3. **Start reviewing** the AI-generated descriptions

### 2. Review Interface
- **Left Panel**: Shows the actual HTML description as it will appear
- **Right Panel**: Review controls and comment area
- **Navigation**: Use Previous/Next buttons or keyboard shortcuts

### 3. Review Actions
- **✅ Approve**: Mark the description as approved and ready to use
- **❌ Reject**: Mark the description as needing revision
- **⏳ Pending**: Mark the description as needing further review
- **💬 Comments**: Add detailed feedback and suggestions

### 4. Keyboard Shortcuts
- `Ctrl + ←` (or `Cmd + ←`): Previous item
- `Ctrl + →` (or `Cmd + →`): Next item
- `Ctrl + S` (or `Cmd + S`): Save current review

### 5. Saving Your Work
- **Auto-save**: Comments are automatically saved as you type
- **Manual save**: Click "💾 Save Review" button
- **Download**: Click "📥 Download Excel" to get updated file with reviews

### 6. Review Columns Added to Excel
When you save reviews, these columns are added to your Excel file:
- **Review Status**: approved/rejected/pending
- **Review Comments**: Your detailed feedback
- **Review Date**: When the review was completed

### 7. Tips for Effective Reviewing
- **Check HTML rendering**: Ensure the description displays correctly
- **Verify content accuracy**: Make sure the AI understood the product correctly
- **Review SEO elements**: Check if keywords are naturally integrated
- **Image analysis**: Verify if image descriptions match the actual product
- **Grammar and style**: Look for any language issues

### 8. Common Review Scenarios

#### ✅ Approve When:
- Description accurately represents the product
- HTML formatting is correct
- SEO keywords are naturally integrated
- Image analysis is accurate and detailed
- Grammar and style are professional

#### ❌ Reject When:
- Major factual errors about the product
- Poor HTML formatting or broken structure
- Keyword stuffing or unnatural language
- Inaccurate image descriptions
- Significant grammar or style issues

#### ⏳ Pending When:
- Minor issues that need clarification
- Waiting for additional product information
- Need to check with product team
- Requires style guide verification

### 9. Troubleshooting
- **File won't load**: Ensure it's a valid Excel file (.xlsx or .xls)
- **No HTML content**: Check that the Excel file has HTML Output column
- **Can't save**: Make sure your browser allows file downloads
- **Keyboard shortcuts don't work**: Try clicking on the page first

### 10. Browser Compatibility
- ✅ **Chrome** (Recommended)
- ✅ **Firefox**
- ✅ **Safari**
- ✅ **Edge**
- ❌ **Internet Explorer** (Not supported)

---

**Need Help?** Contact the development team or refer to the main application documentation.
"""
        
        instructions_path = output_dir / "REVIEW_INSTRUCTIONS.md"
        with open(instructions_path, 'w', encoding='utf-8') as f:
            f.write(instructions_content)
        
        return str(instructions_path)


# Convenience function for easy import
def generate_review_page(excel_file_path: str, output_dir: str = None) -> str:
    """
    Convenience function to generate a review page
    
    Args:
        excel_file_path: Path to the Excel file
        output_dir: Output directory (optional)
        
    Returns:
        str: Path to generated HTML file
    """
    generator = ReviewPageGenerator()
    return generator.generate_review_page(excel_file_path, output_dir)
