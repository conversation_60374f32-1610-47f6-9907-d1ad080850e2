#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEO Manager
SEO 管理器
"""

import csv
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from loguru import logger


class SEOManager:
    """SEO 管理器"""
    
    def __init__(self, seo_dir: str = None):
        self.seo_dir = Path(seo_dir) if seo_dir else None
        self.seo_data = {}  # {filename: [(text, popular), ...]}
        self.load_seo_files()
    
    def set_seo_directory(self, seo_dir: str):
        """設定 SEO 目錄"""
        self.seo_dir = Path(seo_dir)
        self.load_seo_files()
    
    def load_seo_files(self):
        """載入 SEO 檔案"""
        self.seo_data = {}
        
        if not self.seo_dir or not self.seo_dir.exists():
            logger.warning("SEO 目錄不存在")
            return
        
        # 載入所有 CSV 和 TXT 檔案
        for file_path in self.seo_dir.glob("*.csv"):
            self._load_csv_file(file_path)
        
        for file_path in self.seo_dir.glob("*.txt"):
            self._load_txt_file(file_path)
        
        logger.info(f"載入 {len(self.seo_data)} 個 SEO 檔案")
    
    def _load_csv_file(self, file_path: Path):
        """載入 CSV 檔案"""
        try:
            filename = file_path.stem
            seo_items = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader, None)
                
                if not headers:
                    return
                
                # 尋找 text 和 popular 欄位
                text_col = None
                popular_col = None
                
                for i, header in enumerate(headers):
                    if header.lower() in ['text', '文字', '關鍵字']:
                        text_col = i
                    elif header.lower() in ['popular', 'popularity', '熱門度', '重要性']:
                        popular_col = i
                
                if text_col is None:
                    logger.warning(f"CSV 檔案 {file_path} 缺少 text 欄位")
                    return
                
                for row in reader:
                    if len(row) > text_col and row[text_col].strip():
                        text = row[text_col].strip()
                        popular = 1  # 預設重要性
                        
                        if popular_col is not None and len(row) > popular_col:
                            try:
                                popular = float(row[popular_col])
                            except (ValueError, TypeError):
                                popular = 1
                        
                        seo_items.append((text, popular))
            
            # 按重要性排序
            seo_items.sort(key=lambda x: x[1], reverse=True)
            self.seo_data[filename] = seo_items
            
            logger.debug(f"載入 CSV SEO 檔案: {filename} ({len(seo_items)} 項目)")
            
        except Exception as e:
            logger.error(f"載入 CSV 檔案失敗 {file_path}: {e}")
    
    def _load_txt_file(self, file_path: Path):
        """載入 TXT 檔案"""
        try:
            filename = file_path.stem
            seo_items = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):  # 忽略空行和註解
                        # 支援格式: "text,popular" 或只有 "text"
                        if ',' in line:
                            parts = line.split(',', 1)
                            text = parts[0].strip()
                            try:
                                popular = float(parts[1].strip())
                            except (ValueError, TypeError):
                                popular = 1
                        else:
                            text = line
                            popular = 1
                        
                        seo_items.append((text, popular))
            
            # 按重要性排序
            seo_items.sort(key=lambda x: x[1], reverse=True)
            self.seo_data[filename] = seo_items
            
            logger.debug(f"載入 TXT SEO 檔案: {filename} ({len(seo_items)} 項目)")
            
        except Exception as e:
            logger.error(f"載入 TXT 檔案失敗 {file_path}: {e}")
    
    def get_available_seo_types(self) -> List[str]:
        """取得可用的 SEO 類型"""
        return list(self.seo_data.keys())
    
    def get_seo_keywords(self, seo_type: str, max_count: int = 10) -> List[Tuple[str, float]]:
        """取得 SEO 關鍵字"""
        if seo_type not in self.seo_data:
            return []
        
        return self.seo_data[seo_type][:max_count]
    
    def get_seo_text_for_description(self, seo_type: str, max_keywords: int = 5) -> str:
        """為描述取得 SEO 文字"""
        keywords = self.get_seo_keywords(seo_type, max_keywords)
        if not keywords:
            return ""
        
        # 只返回文字部分，按重要性排序
        return ", ".join([kw[0] for kw in keywords])
    
    def preview_seo_file(self, seo_type: str) -> Dict[str, any]:
        """預覽 SEO 檔案內容"""
        if seo_type not in self.seo_data:
            return {
                "exists": False,
                "error": f"SEO 類型 '{seo_type}' 不存在"
            }
        
        keywords = self.seo_data[seo_type]
        file_path = None
        
        # 尋找對應的檔案路徑
        if self.seo_dir:
            for ext in ['.csv', '.txt']:
                potential_path = self.seo_dir / f"{seo_type}{ext}"
                if potential_path.exists():
                    file_path = potential_path
                    break
        
        return {
            "exists": True,
            "file_path": str(file_path) if file_path else "",
            "total_keywords": len(keywords),
            "keywords": keywords[:20],  # 只顯示前 20 個
            "preview": "\n".join([f"{kw[0]} (重要性: {kw[1]})" for kw in keywords[:10]])
        }
    
    def validate_seo_directory(self, seo_dir: str) -> Dict[str, any]:
        """驗證 SEO 目錄"""
        seo_path = Path(seo_dir)
        
        if not seo_path.exists():
            return {
                "valid": False,
                "message": "目錄不存在"
            }
        
        if not seo_path.is_dir():
            return {
                "valid": False,
                "message": "路徑不是目錄"
            }
        
        # 檢查是否有 CSV 或 TXT 檔案
        csv_files = list(seo_path.glob("*.csv"))
        txt_files = list(seo_path.glob("*.txt"))
        
        if not csv_files and not txt_files:
            return {
                "valid": False,
                "message": "目錄中沒有 CSV 或 TXT 檔案"
            }
        
        return {
            "valid": True,
            "csv_files": len(csv_files),
            "txt_files": len(txt_files),
            "total_files": len(csv_files) + len(txt_files)
        }
    
    def get_seo_file_path(self, seo_type: str) -> Optional[str]:
        """取得 SEO 檔案路徑"""
        if not self.seo_dir or seo_type not in self.seo_data:
            return None
        
        for ext in ['.csv', '.txt']:
            file_path = self.seo_dir / f"{seo_type}{ext}"
            if file_path.exists():
                return str(file_path)
        
        return None
