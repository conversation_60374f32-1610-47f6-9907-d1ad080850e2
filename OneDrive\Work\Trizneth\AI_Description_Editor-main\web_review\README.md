# 🌐 AI Description Web Review System

A web-based review system for AI-generated product descriptions that allows you to review HTML content in a browser and save comments back to Excel.

## ✨ Features

- **🖥️ Web Interface**: Clean, responsive design for reviewing HTML content
- **📝 Review Comments**: Add approve/reject/pending status with detailed comments
- **⌨️ Keyboard Shortcuts**: Navigate with Ctrl+Arrow keys, save with Ctrl+S
- **💾 Auto-Save**: Comments automatically saved to Excel file
- **📊 Progress Tracking**: Visual progress bar and item counter
- **🔄 Navigation**: Next/Previous buttons for easy navigation

## 🚀 Quick Start

### From Main Application
1. Process some items in the main AI Description Editor
2. Click the **"🌐 Web 評審系統"** button
3. <PERSON><PERSON><PERSON> will automatically open the review interface

### Manual Launch
```bash
cd web_review
python launch_review.py
```

## 📋 Requirements

Install required packages:
```bash
pip install -r requirements.txt
```

Required packages:
- Flask >= 2.3.0
- Flask-CORS >= 4.0.0
- pandas >= 1.5.0
- openpyxl >= 3.1.0

## 🎯 How to Use

### 1. Review Interface
- **Left Panel**: HTML content preview with proper styling
- **Right Panel**: Review controls and comment area
- **Top Bar**: Progress indicator and item counter

### 2. Review Actions
- **✅ Approve**: Mark content as approved
- **❌ Reject**: Mark content as rejected
- **⏳ Pending**: Mark content as pending review
- **💬 Comments**: Add detailed feedback

### 3. Navigation
- **Previous/Next Buttons**: Navigate between items
- **Keyboard Shortcuts**:
  - `Ctrl + ←`: Previous item
  - `Ctrl + →`: Next item
  - `Ctrl + S`: Save current review

### 4. Auto-Save
- Comments are automatically saved as you type
- Status changes are immediately saved
- All reviews are written back to the Excel file

## 📁 File Structure

```
web_review/
├── review_template.html    # Main web interface
├── review_system.js       # Frontend JavaScript
├── review_server.py       # Backend Flask server
├── launch_review.py       # Launch script
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🔧 Configuration

### Server Settings
- **Default Port**: 8080
- **Auto-open Browser**: Yes
- **CORS Enabled**: Yes for localhost

### Excel Integration
- **Review Columns Added**:
  - `Review Status`: approved/rejected/pending
  - `Review Comments`: Detailed feedback
  - `Review Date`: Timestamp of review

### Custom Launch
```bash
python launch_review.py --excel "your_file.xlsx" --port 8080 --no-browser
```

## 🎨 Interface Features

### Responsive Design
- Works on desktop and tablet devices
- Adaptive layout for different screen sizes
- Professional styling with smooth animations

### Visual Feedback
- Color-coded status buttons
- Progress indicators
- Success/error notifications
- Loading states

### Accessibility
- Keyboard navigation support
- Clear visual hierarchy
- Tooltips and help text

## 💡 Tips

1. **Batch Review**: Use keyboard shortcuts for faster navigation
2. **Detailed Comments**: Provide specific feedback for better results
3. **Status Tracking**: Use different statuses to organize your workflow
4. **Auto-Save**: Don't worry about losing work - everything saves automatically

## 🔍 Troubleshooting

### Common Issues

**Server won't start**:
- Check if port 8080 is available
- Install required packages: `pip install -r requirements.txt`

**Excel file not found**:
- Ensure Excel file is in the current directory
- Check file permissions

**Browser doesn't open**:
- Manually navigate to `http://localhost:8080`
- Check firewall settings

### Debug Mode
```bash
python review_server.py --debug
```

## 🤝 Integration

This system integrates seamlessly with the main AI Description Editor:
- Automatically detects exported Excel files
- Reads HTML content from the appropriate columns
- Saves reviews back to the original Excel file
- Maintains data integrity and formatting

## 📈 Future Enhancements

- [ ] Bulk review actions
- [ ] Review templates
- [ ] Export review summaries
- [ ] Multi-user review support
- [ ] Advanced filtering and search
