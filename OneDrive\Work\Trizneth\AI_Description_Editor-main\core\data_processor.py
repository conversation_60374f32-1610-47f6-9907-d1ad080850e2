#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Processing Module for AI Description Editor
資料處理模組 - 處理 Excel 檔案讀取、驗證和欄位管理
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import re


class ExcelProcessor:
    """Excel 檔案處理器"""
    
    def __init__(self):
        self.data = None
        self.original_data = None
        self.excluded_columns = []
        self.image_column = None
        
    def load_excel(self, file_path: str, sheet_name: str = None) -> bool:
        """
        載入 Excel 檔案
        
        Args:
            file_path: Excel 檔案路徑
            sheet_name: 工作表名稱，None 為第一個工作表
            
        Returns:
            bool: 載入是否成功
        """
        try:
            logger.info(f"載入 Excel 檔案: {file_path}")
            
            if sheet_name:
                self.data = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                self.data = pd.read_excel(file_path)
            
            self.original_data = self.data.copy()
            
            logger.info(f"成功載入 {len(self.data)} 行資料，{len(self.data.columns)} 個欄位")
            logger.debug(f"欄位名稱: {list(self.data.columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"載入 Excel 檔案失敗: {e}")
            return False
    
    def get_sheet_names(self, file_path: str) -> List[str]:
        """
        取得 Excel 檔案中的所有工作表名稱
        
        Args:
            file_path: Excel 檔案路徑
            
        Returns:
            List[str]: 工作表名稱列表
        """
        try:
            excel_file = pd.ExcelFile(file_path)
            return excel_file.sheet_names
        except Exception as e:
            logger.error(f"讀取工作表名稱失敗: {e}")
            return []
    
    def get_columns(self) -> List[str]:
        """取得所有欄位名稱"""
        if self.data is not None:
            return list(self.data.columns)
        return []
    
    def set_excluded_columns(self, columns: List[str]):
        """
        設定要排除的欄位（如 ID、SKU 等參考欄位）
        
        Args:
            columns: 要排除的欄位名稱列表
        """
        self.excluded_columns = columns
        logger.info(f"設定排除欄位: {columns}")
    
    def set_image_column(self, column: str):
        """
        設定圖片欄位
        
        Args:
            column: 圖片欄位名稱
        """
        self.image_column = column
        logger.info(f"設定圖片欄位: {column}")
    
    def get_processing_columns(self) -> List[str]:
        """取得要處理的欄位（排除指定欄位）"""
        if self.data is None:
            return []
        
        all_columns = list(self.data.columns)
        processing_columns = [col for col in all_columns if col not in self.excluded_columns]
        
        return processing_columns
    
    def get_row_data(self, row_index: int) -> Dict[str, Any]:
        """
        取得指定行的資料
        
        Args:
            row_index: 行索引
            
        Returns:
            Dict[str, Any]: 該行的資料字典
        """
        if self.data is None or row_index >= len(self.data):
            return {}
        
        row_data = self.data.iloc[row_index].to_dict()
        
        # 清理 NaN 值
        cleaned_data = {}
        for key, value in row_data.items():
            if pd.isna(value):
                cleaned_data[key] = ""
            else:
                cleaned_data[key] = str(value)
        
        return cleaned_data
    
    def get_processing_data(self, row_index: int) -> Dict[str, Any]:
        """
        取得指定行要處理的資料（排除指定欄位）
        
        Args:
            row_index: 行索引
            
        Returns:
            Dict[str, Any]: 處理用的資料字典
        """
        row_data = self.get_row_data(row_index)
        processing_data = {
            key: value for key, value in row_data.items() 
            if key not in self.excluded_columns
        }
        
        return processing_data
    
    def get_image_filename(self, row_index: int) -> Optional[str]:
        """
        取得指定行的圖片檔名
        
        Args:
            row_index: 行索引
            
        Returns:
            Optional[str]: 圖片檔名，如果沒有則返回 None
        """
        if self.image_column is None or self.data is None:
            return None
        
        if row_index >= len(self.data):
            return None
        
        filename = self.data.iloc[row_index][self.image_column]
        
        if pd.isna(filename):
            return None
        
        return str(filename)
    
    def get_data_range(self, start_row: int, end_row: int) -> pd.DataFrame:
        """
        取得指定範圍的資料
        
        Args:
            start_row: 起始行（包含）
            end_row: 結束行（包含）
            
        Returns:
            pd.DataFrame: 指定範圍的資料
        """
        if self.data is None:
            return pd.DataFrame()
        
        # 確保索引在有效範圍內
        start_row = max(0, start_row)
        end_row = min(len(self.data) - 1, end_row)
        
        return self.data.iloc[start_row:end_row + 1]
    
    def add_output_column(self, column_name: str, data: List[Any]):
        """
        新增輸出欄位
        
        Args:
            column_name: 欄位名稱
            data: 資料列表
        """
        if self.data is None:
            return
        
        if len(data) != len(self.data):
            logger.warning(f"資料長度不符: 期望 {len(self.data)}，實際 {len(data)}")
            return
        
        self.data[column_name] = data
        logger.info(f"新增欄位: {column_name}")
    
    def save_excel(self, file_path: str, include_original: bool = True):
        """
        儲存 Excel 檔案
        
        Args:
            file_path: 儲存路徑
            include_original: 是否包含原始資料
        """
        try:
            if self.data is None:
                logger.error("沒有資料可儲存")
                return False
            
            if include_original and self.original_data is not None:
                # 合併原始資料和新增的欄位
                save_data = self.original_data.copy()
                
                # 新增處理結果欄位
                new_columns = [col for col in self.data.columns if col not in self.original_data.columns]
                for col in new_columns:
                    save_data[col] = self.data[col]
            else:
                save_data = self.data
            
            save_data.to_excel(file_path, index=False)
            logger.info(f"成功儲存檔案: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"儲存檔案失敗: {e}")
            return False
    
    def validate_data(self) -> Dict[str, Any]:
        """
        驗證資料完整性
        
        Returns:
            Dict[str, Any]: 驗證結果
        """
        if self.data is None:
            return {"valid": False, "message": "沒有載入資料"}
        
        result = {
            "valid": True,
            "total_rows": len(self.data),
            "total_columns": len(self.data.columns),
            "empty_rows": 0,
            "missing_values": {},
            "warnings": []
        }
        
        # 檢查空行
        empty_rows = self.data.isnull().all(axis=1).sum()
        result["empty_rows"] = empty_rows
        
        if empty_rows > 0:
            result["warnings"].append(f"發現 {empty_rows} 個空行")
        
        # 檢查缺失值
        for column in self.data.columns:
            missing_count = self.data[column].isnull().sum()
            if missing_count > 0:
                result["missing_values"][column] = missing_count
        
        if result["missing_values"]:
            result["warnings"].append("部分欄位有缺失值")
        
        # 檢查是否有可處理的欄位
        processing_columns = self.get_processing_columns()
        if not processing_columns:
            result["valid"] = False
            result["message"] = "沒有可處理的欄位"
        
        return result


class ImageProcessor:
    """圖片處理器"""

    def __init__(self, pictures_dir: str):
        self.pictures_dir = Path(pictures_dir)
        self.supported_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']

    def find_images(self, filename: str) -> List[str]:
        """
        根據檔名尋找對應的圖片檔案

        Args:
            filename: 基礎檔名

        Returns:
            List[str]: 找到的圖片檔案路徑列表
        """
        if not filename:
            return []

        found_images = []

        # 移除副檔名
        base_name = Path(filename).stem

        # 搜尋所有可能的檔案
        for ext in self.supported_extensions:
            # 完全匹配
            exact_match = self.pictures_dir / f"{base_name}{ext}"
            if exact_match.exists():
                found_images.append(str(exact_match))

            # 模糊匹配（包含檔名的檔案）
            pattern = f"{base_name}*{ext}"
            matches = list(self.pictures_dir.glob(pattern))
            for match in matches:
                if str(match) not in found_images:
                    found_images.append(str(match))

        logger.debug(f"檔名 '{filename}' 找到 {len(found_images)} 張圖片")
        return found_images

    def get_image_info(self, image_path: str) -> Dict[str, Any]:
        """
        取得圖片資訊

        Args:
            image_path: 圖片路徑

        Returns:
            Dict[str, Any]: 圖片資訊
        """
        try:
            from PIL import Image

            with Image.open(image_path) as img:
                return {
                    "path": image_path,
                    "size": img.size,
                    "format": img.format,
                    "mode": img.mode,
                    "file_size": Path(image_path).stat().st_size
                }
        except Exception as e:
            logger.error(f"讀取圖片資訊失敗 {image_path}: {e}")
            return {"path": image_path, "error": str(e)}

    def validate_images_directory(self) -> Dict[str, Any]:
        """
        驗證圖片目錄

        Returns:
            Dict[str, Any]: 驗證結果
        """
        result = {
            "valid": False,
            "directory_exists": False,
            "total_images": 0,
            "supported_formats": {},
            "warnings": []
        }

        if not self.pictures_dir.exists():
            result["warnings"].append(f"圖片目錄不存在: {self.pictures_dir}")
            return result

        result["directory_exists"] = True

        # 統計圖片檔案
        for ext in self.supported_extensions:
            pattern = f"*{ext}"
            files = list(self.pictures_dir.glob(pattern))
            if files:
                result["supported_formats"][ext] = len(files)
                result["total_images"] += len(files)

        if result["total_images"] == 0:
            result["warnings"].append("圖片目錄中沒有找到支援的圖片檔案")
        else:
            result["valid"] = True

        return result
