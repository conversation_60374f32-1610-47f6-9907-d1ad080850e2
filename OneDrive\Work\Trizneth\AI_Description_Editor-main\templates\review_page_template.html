<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Description Review - {export_date}</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .file-input-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-input {
            padding: 8px 12px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 14px;
        }

        .file-input::file-selector-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        .progress-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
            width: 0%;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            min-height: 600px;
        }

        .preview-panel {
            padding: 30px;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto;
            max-height: 80vh;
        }

        .review-panel {
            padding: 30px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .product-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .product-info h3 {
            color: #1976d2;
            margin-bottom: 8px;
        }

        .product-info p {
            color: #666;
            font-size: 14px;
        }

        .html-content {
            background: white;
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            line-height: 1.6;
        }

        .html-content h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .html-content h2 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 22px;
        }

        .html-content h3 {
            color: #7f8c8d;
            margin: 20px 0 10px 0;
            font-size: 18px;
        }

        .html-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .html-content table th,
        .html-content table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .html-content table th {
            background-color: #867e7b;
            color: white;
        }

        .review-form {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .review-section {
            margin-bottom: 25px;
        }

        .review-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .status-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .status-btn {
            padding: 12px;
            border: 2px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-btn.approve {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .status-btn.reject {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .status-btn.pending {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .status-btn.active {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .comment-area {
            flex: 1;
            min-height: 120px;
        }

        .comment-area textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .comment-area textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .nav-btn.prev {
            background: #6c757d;
            color: white;
        }

        .nav-btn.next {
            background: #007bff;
            color: white;
        }

        .nav-btn.save {
            background: #28a745;
            color: white;
        }

        .nav-btn.download {
            background: #17a2b8;
            color: white;
        }

        .nav-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .item-counter {
            font-weight: 600;
            color: #495057;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px;
            border: 1px solid #f5c6cb;
        }

        .welcome-screen {
            text-align: center;
            padding: 60px 40px;
            color: #666;
        }

        .welcome-screen h2 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .welcome-screen .upload-area {
            border: 3px dashed #ddd;
            border-radius: 12px;
            padding: 40px;
            margin: 30px 0;
            transition: all 0.3s ease;
        }

        .welcome-screen .upload-area:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .preview-panel {
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 AI Description Review System</h1>
            <div class="header-controls">
                <div class="file-input-container">
                    <input type="file" id="excelFile" class="file-input" accept=".xlsx,.xls" />
                </div>
                <div class="progress-info">
                    <span id="progressText">Select Excel File</span>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>
        </div>

        <div id="welcomeScreen" class="welcome-screen">
            <h2>📊 Welcome to AI Description Review</h2>
            <p>Select your exported Excel file to start reviewing AI-generated descriptions</p>
            <div class="upload-area">
                <h3>📁 Select Excel File</h3>
                <p>Choose the Excel file containing your AI-generated HTML descriptions</p>
                <p><small>Supported formats: .xlsx, .xls</small></p>
            </div>
            <p><strong>Features:</strong></p>
            <ul style="text-align: left; display: inline-block; margin-top: 15px;">
                <li>✅ Review HTML descriptions in browser</li>
                <li>💬 Add comments and approval status</li>
                <li>💾 Save reviews back to Excel file</li>
                <li>⌨️ Keyboard navigation (Ctrl+Arrow keys)</li>
                <li>📱 Responsive design for all devices</li>
            </ul>
        </div>

        <div id="mainContent" class="main-content" style="display: none;">
            <div class="preview-panel">
                <div class="product-info">
                    <h3 id="productName">Loading...</h3>
                    <p id="productDetails">Excel Row: - | Category: -</p>
                </div>
                
                <div class="html-content" id="htmlContent">
                    <div class="loading">
                        <p>Loading content...</p>
                    </div>
                </div>
            </div>

            <div class="review-panel">
                <div class="review-form">
                    <div class="review-section">
                        <h3>📋 Review Status</h3>
                        <div class="status-buttons">
                            <div class="status-btn approve" data-status="approved">
                                ✅ Approve
                            </div>
                            <div class="status-btn reject" data-status="rejected">
                                ❌ Reject
                            </div>
                            <div class="status-btn pending" data-status="pending">
                                ⏳ Pending
                            </div>
                        </div>
                    </div>

                    <div class="review-section">
                        <h3>💬 Comments</h3>
                        <div class="comment-area">
                            <textarea id="reviewComment" placeholder="Enter your review comments here...&#10;&#10;Examples:&#10;- Great description, approved!&#10;- Need to add more technical details&#10;- Image analysis could be more specific&#10;- Perfect for SEO optimization"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="navigation" class="navigation" style="display: none;">
            <button class="nav-btn prev" id="prevBtn">
                ← Previous
            </button>
            
            <div class="item-counter">
                <span id="itemCounter">Item 1 of 10</span>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button class="nav-btn save" id="saveBtn">
                    💾 Save Review
                </button>
                <button class="nav-btn download" id="downloadBtn">
                    📥 Download Excel
                </button>
                <button class="nav-btn next" id="nextBtn">
                    Next →
                </button>
            </div>
        </div>
    </div>

    <script>
        /**
         * Static HTML Review System - Client-side Excel Processing
         */
        class StaticReviewSystem {
            constructor() {
                this.currentIndex = 0;
                this.items = [];
                this.reviews = {};
                this.workbook = null;
                this.worksheet = null;
                this.fileName = '';

                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                // File input
                this.fileInput = document.getElementById('excelFile');

                // Screens
                this.welcomeScreen = document.getElementById('welcomeScreen');
                this.mainContent = document.getElementById('mainContent');
                this.navigation = document.getElementById('navigation');

                // Navigation elements
                this.prevBtn = document.getElementById('prevBtn');
                this.nextBtn = document.getElementById('nextBtn');
                this.saveBtn = document.getElementById('saveBtn');
                this.downloadBtn = document.getElementById('downloadBtn');

                // Content elements
                this.productName = document.getElementById('productName');
                this.productDetails = document.getElementById('productDetails');
                this.htmlContent = document.getElementById('htmlContent');
                this.reviewComment = document.getElementById('reviewComment');

                // Progress elements
                this.progressText = document.getElementById('progressText');
                this.progressFill = document.getElementById('progressFill');
                this.itemCounter = document.getElementById('itemCounter');

                // Status buttons
                this.statusButtons = document.querySelectorAll('.status-btn');
            }

            bindEvents() {
                // File input change
                this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

                // Navigation events
                this.prevBtn.addEventListener('click', () => this.previousItem());
                this.nextBtn.addEventListener('click', () => this.nextItem());
                this.saveBtn.addEventListener('click', () => this.saveReview());
                this.downloadBtn.addEventListener('click', () => this.downloadExcel());

                // Status button events
                this.statusButtons.forEach(btn => {
                    btn.addEventListener('click', () => this.setStatus(btn.dataset.status));
                });

                // Auto-save on comment change
                this.reviewComment.addEventListener('input', () => {
                    this.autoSaveReview();
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'ArrowLeft':
                                e.preventDefault();
                                this.previousItem();
                                break;
                            case 'ArrowRight':
                                e.preventDefault();
                                this.nextItem();
                                break;
                            case 's':
                                e.preventDefault();
                                this.saveReview();
                                break;
                        }
                    }
                });

                // Drag and drop for file input
                const uploadArea = document.querySelector('.upload-area');
                if (uploadArea) {
                    uploadArea.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        uploadArea.style.borderColor = '#3498db';
                        uploadArea.style.background = '#f8f9fa';
                    });

                    uploadArea.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        uploadArea.style.borderColor = '#ddd';
                        uploadArea.style.background = '';
                    });

                    uploadArea.addEventListener('drop', (e) => {
                        e.preventDefault();
                        uploadArea.style.borderColor = '#ddd';
                        uploadArea.style.background = '';

                        const files = e.dataTransfer.files;
                        if (files.length > 0) {
                            this.fileInput.files = files;
                            this.handleFileSelect({ target: { files: files } });
                        }
                    });

                    uploadArea.addEventListener('click', () => {
                        this.fileInput.click();
                    });
                }
            }

            async handleFileSelect(event) {
                const file = event.target.files[0];
                if (!file) return;

                this.fileName = file.name;
                this.progressText.textContent = 'Loading Excel file...';

                try {
                    const data = await this.readExcelFile(file);
                    this.processExcelData(data);
                    this.showMainInterface();
                    this.displayCurrentItem();
                    this.updateProgress();
                } catch (error) {
                    this.showError(`Error reading Excel file: ${error.message}`);
                }
            }

            readExcelFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            resolve(workbook);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('Failed to read file'));
                    reader.readAsArrayBuffer(file);
                });
            }

            processExcelData(workbook) {
                this.workbook = workbook;
                const sheetName = workbook.SheetNames[0];
                this.worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(this.worksheet, { header: 1 });

                if (jsonData.length < 2) {
                    throw new Error('Excel file appears to be empty or invalid');
                }

                // Find columns
                const headers = jsonData[0];
                const htmlColumnIndex = this.findColumn(headers, ['html output', 'html', 'description html']);
                const nameColumnIndex = this.findColumn(headers, ['product name', 'title', 'name']);
                const categoryColumnIndex = this.findColumn(headers, ['category', 'type']);
                const reviewStatusIndex = this.findColumn(headers, ['review status']);
                const reviewCommentsIndex = this.findColumn(headers, ['review comments']);

                if (htmlColumnIndex === -1) {
                    throw new Error('No HTML content column found. Looking for columns containing "HTML Output", "HTML", or "Description HTML"');
                }

                // Process rows
                this.items = [];
                for (let i = 1; i < jsonData.length; i++) {
                    const row = jsonData[i];
                    const htmlContent = row[htmlColumnIndex];

                    if (htmlContent && htmlContent.trim()) {
                        this.items.push({
                            rowIndex: i,
                            productName: row[nameColumnIndex] || `Item ${i}`,
                            category: row[categoryColumnIndex] || 'General',
                            htmlContent: htmlContent,
                            originalRow: row,
                            reviewStatus: row[reviewStatusIndex] || 'pending',
                            reviewComments: row[reviewCommentsIndex] || ''
                        });

                        // Load existing review
                        this.reviews[this.items.length - 1] = {
                            status: row[reviewStatusIndex] || 'pending',
                            comment: row[reviewCommentsIndex] || ''
                        };
                    }
                }

                if (this.items.length === 0) {
                    throw new Error('No items with HTML content found');
                }

                console.log(`Loaded ${this.items.length} items from Excel file`);
            }

            findColumn(headers, searchTerms) {
                for (let term of searchTerms) {
                    const index = headers.findIndex(header =>
                        header && header.toString().toLowerCase().includes(term.toLowerCase())
                    );
                    if (index !== -1) return index;
                }
                return -1;
            }

            showMainInterface() {
                this.welcomeScreen.style.display = 'none';
                this.mainContent.style.display = 'grid';
                this.navigation.style.display = 'flex';
            }

            displayCurrentItem() {
                if (this.items.length === 0) {
                    this.showError('No items to review');
                    return;
                }

                const item = this.items[this.currentIndex];
                const review = this.reviews[this.currentIndex] || {};

                // Update product information
                this.productName.textContent = item.productName;
                this.productDetails.textContent = `Excel Row: ${item.rowIndex + 1} | Category: ${item.category}`;

                // Display HTML content
                this.htmlContent.innerHTML = item.htmlContent;

                // Load existing review
                this.reviewComment.value = review.comment || '';
                this.setStatus(review.status || 'pending');

                // Update navigation buttons
                this.prevBtn.disabled = this.currentIndex === 0;
                this.nextBtn.disabled = this.currentIndex === this.items.length - 1;
            }

            setStatus(status) {
                // Remove active class from all buttons
                this.statusButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to selected button
                const selectedBtn = document.querySelector(`[data-status="${status}"]`);
                if (selectedBtn) {
                    selectedBtn.classList.add('active');
                }

                // Store status
                if (!this.reviews[this.currentIndex]) {
                    this.reviews[this.currentIndex] = {};
                }
                this.reviews[this.currentIndex].status = status;

                // Auto-save
                this.autoSaveReview();
            }

            previousItem() {
                if (this.currentIndex > 0) {
                    this.saveCurrentReview();
                    this.currentIndex--;
                    this.displayCurrentItem();
                    this.updateProgress();
                }
            }

            nextItem() {
                if (this.currentIndex < this.items.length - 1) {
                    this.saveCurrentReview();
                    this.currentIndex++;
                    this.displayCurrentItem();
                    this.updateProgress();
                }
            }

            saveCurrentReview() {
                if (!this.reviews[this.currentIndex]) {
                    this.reviews[this.currentIndex] = {};
                }

                this.reviews[this.currentIndex].comment = this.reviewComment.value;
                this.reviews[this.currentIndex].timestamp = new Date().toISOString();
            }

            saveReview() {
                this.saveCurrentReview();
                this.updateExcelData();
                this.showNotification('Review saved successfully!', 'success');
            }

            autoSaveReview() {
                // Debounced auto-save
                clearTimeout(this.autoSaveTimeout);
                this.autoSaveTimeout = setTimeout(() => {
                    this.saveCurrentReview();
                    this.updateExcelData();
                }, 1000);
            }

            updateExcelData() {
                if (!this.worksheet) return;

                const headers = XLSX.utils.sheet_to_json(this.worksheet, { header: 1 })[0];

                // Find or create review columns
                let reviewStatusIndex = this.findColumn(headers, ['review status']);
                let reviewCommentsIndex = this.findColumn(headers, ['review comments']);
                let reviewDateIndex = this.findColumn(headers, ['review date']);

                // Add columns if they don't exist
                if (reviewStatusIndex === -1) {
                    reviewStatusIndex = headers.length;
                    headers.push('Review Status');
                }
                if (reviewCommentsIndex === -1) {
                    reviewCommentsIndex = headers.length;
                    headers.push('Review Comments');
                }
                if (reviewDateIndex === -1) {
                    reviewDateIndex = headers.length;
                    headers.push('Review Date');
                }

                // Update worksheet with new headers
                XLSX.utils.sheet_add_aoa(this.worksheet, [headers], { origin: 'A1' });

                // Update review data
                this.items.forEach((item, index) => {
                    const review = this.reviews[index];
                    if (review) {
                        const cellRow = item.rowIndex + 1; // +1 because XLSX is 1-indexed

                        // Update cells
                        const statusCell = XLSX.utils.encode_cell({ r: cellRow, c: reviewStatusIndex });
                        const commentsCell = XLSX.utils.encode_cell({ r: cellRow, c: reviewCommentsIndex });
                        const dateCell = XLSX.utils.encode_cell({ r: cellRow, c: reviewDateIndex });

                        this.worksheet[statusCell] = { v: review.status || 'pending' };
                        this.worksheet[commentsCell] = { v: review.comment || '' };
                        this.worksheet[dateCell] = { v: new Date().toLocaleString() };
                    }
                });
            }

            downloadExcel() {
                try {
                    this.updateExcelData();

                    // Generate new filename
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    const newFileName = this.fileName.replace(/\.xlsx?$/i, `_reviewed_${timestamp}.xlsx`);

                    // Write and download
                    const wbout = XLSX.write(this.workbook, { bookType: 'xlsx', type: 'array' });
                    const blob = new Blob([wbout], { type: 'application/octet-stream' });

                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = newFileName;
                    link.click();

                    this.showNotification(`Excel file downloaded: ${newFileName}`, 'success');
                } catch (error) {
                    this.showError(`Failed to download Excel file: ${error.message}`);
                }
            }

            updateProgress() {
                const progress = ((this.currentIndex + 1) / this.items.length) * 100;
                this.progressFill.style.width = `${progress}%`;

                const progressText = `Item ${this.currentIndex + 1} of ${this.items.length}`;
                this.progressText.textContent = progressText;
                this.itemCounter.textContent = progressText;
            }

            showError(message) {
                this.htmlContent.innerHTML = `
                    <div class="error">
                        <h3>❌ Error</h3>
                        <p>${message}</p>
                        <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            🔄 Reload Page
                        </button>
                    </div>
                `;
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // Initialize the review system when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.reviewSystem = new StaticReviewSystem();
        });
    </script>
</body>
</html>
