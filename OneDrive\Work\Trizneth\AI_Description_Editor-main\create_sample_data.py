#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Sample Data for AI Description Editor
建立範例資料用於測試 AI 商品描述編輯器
"""

import pandas as pd
from pathlib import Path


def create_sample_excel():
    """建立範例 Excel 檔案"""
    
    # 範例產品資料
    sample_data = [
        {
            "ID": "PROD001",
            "Product Name": "Swisse Vitamin C 1000mg",
            "Category": "Immunity",
            "Brand": "Swisse",
            "Description": "高效維生素C補充劑，增強免疫力",
            "Main Ingredients": "維生素C 1000mg, 生物類黃酮, 玫瑰果萃取",
            "Benefits": "增強免疫系統, 抗氧化, 促進膠原蛋白合成",
            "Usage": "每日1粒，餐後服用",
            "Warnings": "孕婦哺乳期請諮詢醫師",
            "Country": "澳洲",
            "Image": "swisse_vitamin_c.jpg"
        },
        {
            "ID": "PROD002", 
            "Product Name": "Nordic Naturals Omega-3",
            "Category": "Immunity",
            "Brand": "Nordic Naturals",
            "Description": "純淨魚油Omega-3，支持心血管健康",
            "Main Ingredients": "EPA 325mg, DHA 225mg, 維生素E",
            "Benefits": "支持心血管健康, 促進大腦發育, 抗發炎",
            "Usage": "每日2粒，隨餐服用",
            "Warnings": "對魚類過敏者請勿使用",
            "Country": "美國",
            "Image": "nordic_omega3.jpg"
        },
        {
            "ID": "PROD003",
            "Product Name": "IKEA POÄNG 休閒椅",
            "Category": "Furniture", 
            "Brand": "IKEA",
            "Description": "經典北歐風格休閒椅，舒適耐用",
            "Main Ingredients": "樺木夾板, 聚氨酯泡棉, 棉質布套",
            "Benefits": "人體工學設計, 可拆洗布套, 環保材質",
            "Usage": "適合客廳、臥室休憩使用",
            "Warnings": "最大承重120公斤",
            "Country": "瑞典",
            "Image": "ikea_poang_chair.jpg"
        },
        {
            "ID": "PROD004",
            "Product Name": "Herman Miller Aeron 辦公椅",
            "Category": "Furniture",
            "Brand": "Herman Miller", 
            "Description": "頂級人體工學辦公椅，專業辦公首選",
            "Main Ingredients": "鋁合金框架, 透氣網布, 聚氨酯扶手",
            "Benefits": "12年保固, 透氣舒適, 支撐腰椎",
            "Usage": "辦公室、書房長時間工作使用",
            "Warnings": "需專業安裝，重量25公斤",
            "Country": "美國",
            "Image": "herman_miller_aeron.jpg"
        },
        {
            "ID": "PROD005",
            "Product Name": "Blackmores Bio C 1000",
            "Category": "Immunity",
            "Brand": "Blackmores",
            "Description": "天然維生素C，溫和不刺激腸胃",
            "Main Ingredients": "天然維生素C 1000mg, 生物類黃酮, 針葉櫻桃",
            "Benefits": "溫和配方, 長效釋放, 增強抵抗力",
            "Usage": "每日1-2粒，空腹或餐後皆可",
            "Warnings": "兒童請減半使用",
            "Country": "澳洲",
            "Image": "blackmores_bio_c.jpg"
        }
    ]
    
    # 建立 DataFrame
    df = pd.DataFrame(sample_data)
    
    # 儲存為 Excel 檔案
    output_path = Path("sample_products.xlsx")
    df.to_excel(output_path, index=False)
    
    print(f"範例 Excel 檔案已建立: {output_path}")
    print(f"包含 {len(sample_data)} 個產品資料")
    
    return output_path


def create_sample_keywords():
    """建立範例關鍵字檔案"""
    
    # 確保目錄存在
    categories_dir = Path("categories")
    
    # 更新免疫力關鍵字
    immunity_keywords = [
        "免疫力", "抵抗力", "防護力", "維生素C", "維他命C",
        "天然", "健康", "營養", "補充", "保健",
        "抗氧化", "增強體質", "提升免疫", "強化防禦",
        "澳洲", "美國", "進口", "品質保證", "安全",
        "每日", "餐後", "服用", "建議", "劑量"
    ]
    
    immunity_file = categories_dir / "Immunity" / "keywords.txt"
    immunity_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(immunity_file, 'w', encoding='utf-8') as f:
        for keyword in immunity_keywords:
            f.write(f"{keyword}\n")
    
    # 更新傢俱關鍵字
    furniture_keywords = [
        "傢俱", "家具", "椅子", "辦公椅", "休閒椅",
        "人體工學", "舒適", "耐用", "設計", "北歐風格",
        "客廳", "臥室", "辦公室", "書房", "居家",
        "品質", "保固", "安裝", "材質", "環保",
        "承重", "尺寸", "顏色", "風格", "實用"
    ]
    
    furniture_file = categories_dir / "Furniture" / "keywords.txt"
    furniture_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(furniture_file, 'w', encoding='utf-8') as f:
        for keyword in furniture_keywords:
            f.write(f"{keyword}\n")
    
    print("範例關鍵字檔案已更新")


def create_sample_prompts():
    """建立範例 Prompt 檔案"""
    
    # 建立藥妝專用 Writer Prompt
    pharmacy_writer = """你是專業的藥妝產品描述撰寫專家。請根據提供的產品資料，撰寫一個專業、吸引人且符合 SEO 的 HTML 產品描述。

## 撰寫要求：
1. 使用繁體中文撰寫
2. 強調產品的健康效益和安全性
3. 自然融入提供的關鍵字
4. 遵循嚴格的 HTML 結構格式
5. 內容要專業可信，避免誇大宣傳

## 必須遵循的 HTML 結構：
```html
<h1>[產品名稱]</h1>

<h2>Description</h2>
<h3>Product Overview</h3>
[產品概述，包含品牌、來源國、主要特色]

<h3>Main Benefits</h3>
[主要效益，列出3-5個核心好處]

<h2>Ingredients</h2>
<h3>Active Ingredients</h3>
[活性成分詳細說明]

<h3>Free From / Allergy Info</h3>
[過敏資訊和不含成分說明]

<h2>How to Use</h2>
<h3>Dosage</h3>
[詳細的使用方法和劑量]

<h3>Usage Warnings</h3>
[使用注意事項和警告]

<h2>Additional Information</h2>
<h3>Miscellaneous</h3>
[其他相關資訊，如保存方式、認證等]
```

## 產品資料：
{product_data}

## 關鍵字（請自然融入）：
{keywords}

請嚴格按照上述 HTML 結構撰寫產品描述。"""
    
    prompts_dir = Path("prompts")
    writer_dir = prompts_dir / "writer"
    writer_dir.mkdir(parents=True, exist_ok=True)
    
    with open(writer_dir / "pharmacy.txt", 'w', encoding='utf-8') as f:
        f.write(pharmacy_writer)
    
    # 建立傢俱專用 Writer Prompt
    furniture_writer = """你是專業的傢俱產品描述撰寫專家。請根據提供的產品資料，撰寫一個吸引人且實用的 HTML 產品描述。

## 撰寫要求：
1. 使用繁體中文撰寫
2. 強調產品的設計美學和實用性
3. 突出材質品質和工藝特色
4. 自然融入提供的關鍵字
5. 遵循嚴格的 HTML 結構格式

## 必須遵循的 HTML 結構：
```html
<h1>[產品名稱]</h1>

<h2>Description</h2>
<h3>Product Overview</h3>
[產品概述，包含設計風格、適用空間、主要特色]

<h3>Main Benefits</h3>
[主要優勢，如舒適度、耐用性、美觀性等]

<h2>Ingredients</h2>
<h3>Active Ingredients</h3>
[材質說明，包含框架、填充物、表面材質等]

<h3>Free From / Allergy Info</h3>
[環保認證、無毒材質、過敏注意事項]

<h2>How to Use</h2>
<h3>Dosage</h3>
[使用建議、適用場所、搭配建議]

<h3>Usage Warnings</h3>
[使用注意事項、承重限制、保養須知]

<h2>Additional Information</h2>
<h3>Miscellaneous</h3>
[尺寸規格、保固資訊、安裝說明等]
```

## 產品資料：
{product_data}

## 關鍵字（請自然融入）：
{keywords}

請嚴格按照上述 HTML 結構撰寫產品描述。"""
    
    with open(writer_dir / "furniture.txt", 'w', encoding='utf-8') as f:
        f.write(furniture_writer)
    
    print("範例 Prompt 檔案已建立")


if __name__ == "__main__":
    print("建立 AI 商品描述編輯器範例資料...")
    
    # 建立範例 Excel 檔案
    excel_path = create_sample_excel()
    
    # 建立範例關鍵字
    create_sample_keywords()
    
    # 建立範例 Prompts
    create_sample_prompts()
    
    print("\n✅ 範例資料建立完成！")
    print("\n📋 使用說明：")
    print("1. 執行 python main.py 啟動程式")
    print("2. 載入 sample_products.xlsx 檔案")
    print("3. 設定 API 金鑰在 config/ai_keys.env")
    print("4. 選擇適當的 Prompt（pharmacy 或 furniture）")
    print("5. 開始處理並查看結果")
